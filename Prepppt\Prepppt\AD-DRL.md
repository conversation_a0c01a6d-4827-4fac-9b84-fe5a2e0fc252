---
marp: true
# theme: suibeppt
math: mathjax
style: |
  .authors-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin: 30px 0;
    text-align: center;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
  }
  .author {
    padding: 10px;
    border-radius: 8px;
    /* background-color: rgba(240, 240, 240, 0.3); */
    /* border: 1px solid rgba(200, 200, 200, 0.5); */
  }
  .author-name {
    font-weight: bold;
    margin-bottom: 8px;
    font-size: 1.1em;
    color: #333;
  }
  .author-info {
    font-size: 0.9em;
    line-height: 1.3;
    color: #555;
  }
  /* 强制网格布局 */
  .authors-grid > .author:nth-child(1) { grid-column: 1; grid-row: 1; }
  .authors-grid > .author:nth-child(2) { grid-column: 2; grid-row: 1; }
  .authors-grid > .author:nth-child(3) { grid-column: 3; grid-row: 1; }
  .authors-grid > .author:nth-child(4) { grid-column: 1; grid-row: 2; }
  .authors-grid > .author:nth-child(5) { grid-column: 2; grid-row: 2; }
  .authors-grid > .author:nth-child(6) { grid-column: 3; grid-row: 2; }
  /* 响应式设计：在较小屏幕上改为2列 */
  @media (max-width: 800px) {
    .authors-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
    }
    .authors-grid > .author:nth-child(1) { grid-column: 1; grid-row: 1; }
    .authors-grid > .author:nth-child(2) { grid-column: 2; grid-row: 1; }
    .authors-grid > .author:nth-child(3) { grid-column: 1; grid-row: 2; }
    .authors-grid > .author:nth-child(4) { grid-column: 2; grid-row: 2; }
    .authors-grid > .author:nth-child(5) { grid-column: 1; grid-row: 3; }
    .authors-grid > .author:nth-child(6) { grid-column: 2; grid-row: 3; }
  }
  /* 在很小的屏幕上改为1列 */
  @media (max-width: 500px) {
    .authors-grid {
      grid-template-columns: 1fr;
    }
    .authors-grid > .author { grid-column: 1 !important; }
  }

  /* 强制所有页面内容顶部对齐 */
  section {
    justify-content: flex-start !important;
    align-items: flex-start !important;
    display: flex !important;
    flex-direction: column !important;
    padding-top: 50px !important;
  }

  /* 首页标题居中 */
  section:first-of-type h1 {
    text-align: center !important;
    width: 100% !important;
  }

  /* 确保段落从顶部开始 */
  section > * {
    margin-top: 0 !important;
  }

  /* 为内容少的页面添加最小高度 */
  section p {
    min-height: auto;
  }

  /* 只针对块级数学公式($$...$$)居中显示，不影响行内公式($...$) */

  /* KaTeX 块级公式居中 */
  .katex-display {
    text-align: center !important;
    margin: 1.5em auto !important;
    display: block !important;
  }

  /* MathJax 块级公式居中 */
  .MathJax_Display {
    text-align: center !important;
    margin: 1.5em auto !important;
    display: block !important;
  }

  /* 现代 MathJax 块级公式居中 */
  mjx-container[display="true"] {
    text-align: center !important;
    margin: 1.5em auto !important;
    display: block !important;
  }

  /* 通用块级数学公式类 */
  .math-display,
  .math.display {
    text-align: center !important;
    margin: 1.5em auto !important;
    display: block !important;
  }

  /* 确保在section中的块级公式居中 */
  section .katex-display,
  section .MathJax_Display,
  section mjx-container[display="true"] {
    text-align: center !important;
  }

  /* 不影响行内公式 */
  .katex:not(.katex-display),
  .MathJax:not(.MathJax_Display),
  mjx-container:not([display="true"]) {
    display: inline !important;
    text-align: inherit !important;
  }

  /* 参考suibeppt.css的数学公式居中方法 */
  .MathJax,
  .MathJax_Display,
  mjx-container[display="true"],
  mjx-container[jax="CHTML"][display="true"],
  .mjx-chtml[display="true"] {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    display: block !important;
    width: 100% !important;
    justify-self: center !important;
    align-self: center !important;
  }

  /* 行内数学公式保持原样 */
  mjx-container[display="false"],
  mjx-container[jax="CHTML"][display="false"],
  .mjx-chtml[display="false"] {
    display: inline !important;
    text-align: inherit !important;
  }

  /* 确保包含$$公式的段落居中 */
  p:has(mjx-container[display="true"]),
  div:has(mjx-container[display="true"]),
  section p:has(mjx-container[display="true"]),
  section div:has(mjx-container[display="true"]) {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    width: 100% !important;
  }

---

# 面向多模态推荐的属性驱动解耦表征学习

# Attribute-driven Disentangled Representation Learning for Multimodal Recommendation 


<div class="authors-grid">
<div class="author">
<div class="author-name">李振阳</div>
<div class="author-info">
山东大学<br>
中国青岛<br>
<EMAIL>
</div>
</div>

<div class="author">
<div class="author-name">刘凡*</div>
<div class="author-info">
新加坡国立大学<br>
新加坡<br>
<EMAIL>
</div>
</div>

<div class="author">
<div class="author-name">魏因伟</div>
<div class="author-info">
莫纳什大学<br>
澳大利亚墨尔本<br>
<EMAIL>
</div>
</div>

<div class="author">
<div class="author-name">程志勇</div>
<div class="author-info">
合肥工业大学<br>
中国·合肥<br>
<EMAIL>
</div>
</div>

<div class="author">
<div class="author-name">聂礼强*</div>
<div class="author-info">
哈尔滨工业大学<br>
深圳<br>
中国·深圳<br>
<EMAIL>
</div>
</div>

<div class="author">
<div class="author-name">Mohan Kankanhalli</div>
<div class="author-info">
新加坡国立大学<br>
新加坡<br>
<EMAIL>
</div>
</div>
</div>

---

## 摘要

推荐算法通过关联从历史交互模式中提取的用户与物品表征来预测用户偏好。为提升性能,现有方法多采用无监督方式解耦跨模态交互数据中的复杂因素,以学习鲁棒且独立的表征。然而,这种方法模糊了特定因素(如品类或品牌)对结果影响的辨识度,导致难以调控其作用。针对这一挑战,我们提出属性驱动解耦表征学习(AD-DRL)新方法,将多模态属性显式融入解耦学习过程。通过为多模态特征中的每个因素分配特定属性,AD-DRL能在属性与属性值双层级实现因素解耦。为获得各属性关联因素的鲁棒独立表征,我们首先解耦模态内与跨模态的特征表征,继而通过融合同一因素的多模态特征进一步增强表征鲁棒性。在三个真实场景公开数据集上的实证评估验证了AD-DRL的有效性、可解释性与可控性。

---

## 1 引言

推荐系统(RS)是电子商务到广告等众多在线平台的核心组件,通过帮助用户精准匹配符合偏好的项目发挥关键作用。鉴于其重要性,学界投入大量精力开发高级推荐模型以提升性能。其中,基于协同过滤(CF)的模型通过利用用户-项目交互数据学习表征取得显著成功。然而这些模型因完全依赖交互数据,在实际场景中极易遭遇稀疏性问题。为缓解推荐中的数据稀疏问题,通常利用包含用户/项目丰富信息的辅助数据(如属性、用户评论和商品图像)来增强表征学习。

学界普遍认为,用户与项目的纠缠式表征无法直接捕捉跨维度细粒度用户偏好,从而制约推荐系统的效能与可解释性。近年来,解耦表征学习因其能识别并分离数据背后的潜在因子,在计算机视觉等多个领域获得广泛关注。实证研究表明,解耦表征在复杂应用场景中展现出更强的鲁棒性。因此众多推荐方法采用解耦表征学习技术,以获取稳健且独立的表征,最终提升推荐性能。例如Ma等[25]利用解耦表征捕捉用户意图相关不同概念下的偏好;等提出基于GCN的模型,通过建模用户-项目交互的意图分布生成解耦表征,探索多样性。

---

关于用户采纳项目意图的研究。然而,这些方法仅专注于基于用户和项目的ID嵌入来解耦其表征。为挖掘多模态数据背后影响因素的差异,Liu等人[19]通过复杂的注意力驱动模块,估算了用户对不同模态潜在因素的注意力权重。 [cite: 3]

尽管解耦技术为推荐系统带来了显著进步,但现有研究通常将用户和项目都分解为潜在因子。这种方法本质上限制了模型的可解释性与可控性。例如,尝试用解耦表征来解释影响用户选择连衣裙的多元因素(如款式、品牌、流行度和价格)时,当前解耦方法提取的潜在因子具有固有抽象性,难以确定每个因子具体对应哪些服装属性。尤其当某个因子可能模糊关联品牌与价格的组合,而另一因子又混合体现款式与流行度时,这种潜在因子的模糊性会削弱推荐系统的可解释性,使我们难以理解为何向用户推荐特定商品。更关键的是,这种不明确性还会影响推荐系统的可控性——假设用户希望获得不考虑价格和流行度的连衣裙推荐时,由于潜在因子未明确关联这些属性,调整推荐系统聚焦特定偏好将极具挑战性。

---

物品属性通过多模态形式呈现,能为推荐系统提供多样化且互补的信息。例如文本数据可能明确提及品牌或价格,而视觉数据可揭示物品类别等视觉属性。此外,物品热度可从交互数据的统计信息中推导。本质上,属性代表着物品具体且有意义的特性。因此,利用属性指导解耦过程,能有效提升传统多模态推荐方法的可解释性与可控性。本文提出属性驱动的解耦表征学习方法(简称AD-DRL),在多层次属性粒度上解耦用户与物品的多模态表征。为获得与特定属性相关的稳健独立表征,我们首先解耦模态内及跨模态的特征表征,该过程由高级属性(如类别、热度)引导以揭示因子间潜在关联。继而通过融合同一因子的多模态特征增强表征鲁棒性,此步骤利用低级属性(如服装数据集中的具体类别值:牛仔裤、夹克、连衣裙)挖掘同因子表征间关系,从而获得更细粒度、更全面的解耦表征。我们在三个真实数据集上进行大量实验与消融研究,结果验证了AD-DRL方法的优越性,并展示了其在可解释性与可控性方面的能力。 

---

## 2 相关工作

### 2.1 多模态协同过滤

传统协同过滤(CF)[5,31,32]方法主要依赖用户-物品交互数据来学习用户和物品的表征。当遇到交互数据有限的用户和物品时,推荐性能会显著下降。为缓解数据稀疏性[16,20,26,33]带来的挑战,近期研究将多模态信息引入推荐系统。评论、图像等多模态特征 [4, 8, 10, 19, 21, 41,42,45]能为用户偏好和物品特性提供有价值的信息,这些特征可以补充历史用户-物品交互数据,从而提升推荐性能。 

先前研究以直接方式将多模态信息整合至基于矩阵分解的方法中。例如VBPR[10]直接将商品图像提取的视觉特征与协同特征拼接为联合商品表征,并输入矩阵分解模块。随着深度学习技术在建模复杂交互行为[12]和多模态特征间关系[28]方面的成功,大量深度学习技术被引入多模态推荐系统[12,34]。如MAML[21]先融合商品多模态特征与用户特征,再通过注意力模块捕捉用户多样化偏好;VECF[1]则利用多模态注意力网络构建视觉可解释的协同过滤模型,促进不同特征模态的深度融合。

---

近年来,图卷积网络(GCNs)[9]在推荐系统的表征学习方面展现出强大能力[11,38,40,47]。基于GCN结构,MMGCN[42]构建用户-商品二分图学习各模态表征,继而融合为最终表征;GRCN[41]则利用商品多模态内容优化交互图结构,以减轻假阳性边对推荐性能的影响。

---

### 2.2 解耦表征学习

解耦表征学习旨在识别并分离数据中的潜在解释因子,已在计算机视觉领域引起广泛关注[6,13,24]。例如beta-VAE方法[13]采用约束变分框架学习基础视觉概念的解耦表征;而IPGDN[24]则能自动发现图数据中存在的独立潜在因子。 

由于解耦表征学习在多个领域的成功应用,近年来大量研究聚焦于为推荐系统中的用户和物品学习解耦表征 [19,25,27,35,37,39]。受计算机视觉领域先前工作的启发,初期尝试采用变分自编码器(VAE) [18]来学习解耦表征。例如MacridVAE[25]分别捕获了用户针对不同意图概念的偏好。除基于用户-物品交互建模得到的解耦表征外,ADDVAE [35]还从文本内容中获取另一组解耦用户表征,并将两组表征进行对齐。 

---

为研究用户采用物品的意图多样性,DGCF[39]采用图解耦模块迭代优化意图感知的交互图和因子表示以生成推荐。为建模用户对各模态不同因素的多元偏好,DRML [19]通过注意力模块估计用户对不同模态潜在因素的注意力权重。由于所学解耦表征缺乏明确语义,KDR[27]利用知识图谱(KGs)指导学习过程,确保生成的解耦表征与从KGs提取的语义信息相关联。

尽管现有模型通过解耦表征学习实现了性能提升,但都面临将用户和物品表征解耦为潜在因子却未阐明各因子语义的问题。 

---

## 3 方法

### 3.1 预备知识

#### 3.1.1 问题设定。
给定用户集 $\mathcal{U}\in\{u\}$ 与物品集 $I\in\{i\}$, 我们利用三类信息学习用户与物品表征:(1)用户-物品交互矩阵R,其中每个条目 $r_{ui}\in R$ 表示用户对物品的隐式反馈(如点击、点赞或购买),与物品i 存在交互时记为 $r_{ui}=1$,观察数据中无交互时记为 $r_{ui}=0$ ; (2)多模态特征,主要为物品关联的评论文本与图像两类特征; [cite: 5] (3)属性信息,包含K个物品属性及其关联属性值,作为解耦表征学习的监督信号。我们的目标是学习用户与物品的鲁棒表征,从而预测用户对未交互物品的偏好。 

---

#### 3.1.2 符号约定。
延续先前研究 $[19,41]$ 的做法,每个用户和物品均被分配唯一ID,并分别用随机初始化的向量 $v_{u}\in\mathbb{R}^{d}$ 和 $v_{i}\in\mathbb{R}^{d}$ 表示。针对评论文本与图像信息,我们分别采用BERT[2] 和ViT[3]模型提取原始文本特征 $e_{t}\in\mathbb{R}^{d_{0}}$ 与视觉特征 $e_{v}\in\mathbb{R}^{d_{0}}$。为构建推荐导向的特征表示,通过两个非线性变换将 $e_{t}$ 和 $e_{v}$ 映射至同一特征空间:


$$
\upsilon_{t}=\sigma(W_{t}e_{t}+b_{t}), \\
\nu_{v}=\sigma(W_{v}e_{v}+b_{v}), \quad (1)
$$



其中 $W_{t},W_{v}\in\mathbb{R}^{d\times d_{0}}$ 和 $b_{t},b_{v}\in\mathbb{R}^{d}$ 分别表示文本模态和视觉模态的权重矩阵与偏置向量, $\sigma(\cdot)$ 为激活函数。 

依据先前工作 [19,39] 中的解耦表征学习方法,我们将特征向量分割为K个区块,每个区块对应特定商品属性(如价格、品牌)。为简化处理,将各模态表征均等划分为K个连续区块。以商品ID嵌入为例: 

$$ 
v_{i}=(v_{i}^{1},v_{i}^{2},\cdot\cdot\cdot,v_{i}^{K}) \quad (2)
$$  

其中 $v_{i}^{k}\in\mathbb{R}^{\frac{d}{k}}$ 表示对应于第k个属性的物品ID嵌入。

---

类似地, $v_{t}=(v_{t}^{1},v_{t}^{2},\cdot\cdot\cdot,v_{t}^{K}),v_{v}=(v_{v}^{1},v_{v}^{2},\cdot\cdot\cdot,v_{v}^{K})$ $v_{u}=(v_{u}^{1},v_{u}^{2},\cdot\cdot\cdot,v_{u}^{K})$ 分别定义为文本特征嵌入、视觉特征嵌入和用户ID嵌入。 

---

#### 3.1.3 属性驱动解缠的直观理解。
我们的工作不仅旨在为用户推荐精准项目,更倡导在多模态推荐系统中采用属性驱动解缠机制,以增强推荐结果的可解释性与可控性。具体而言,与现有无监督解缠潜在因子的方法不同,我们利用物品属性的语义标签,为每个属性学习专属子空间以获得解缠表征。本文中,各模态向量由K个区块构成,并假设每个区块关联特定属性(如价格或品牌)。实现属性驱动解缠需满足两个必要条件:其一,每个区块应具有明确的属性指向性,且不同属性对应的区块需可区分。例如区块关联价格,而区块 k表征品牌向量;其二,特定属性的每个区块需精确捕捉该属性值。例如区块应准确反映产品的价格水平(即昂贵或廉价)。 [cite: 4]

---

### 3.2 属性驱动解缠表征学习

本节详细阐述我们提出的AD-DRL模型(属性驱动解耦表征学习模型)。该模型通过为多模态特征中的每个因子分配特定属性,旨在提升推荐模型的可解释性与可控性。具体而言,AD-DRL包含两个解耦模块:高层属性驱动解耦表征学习和低层属性驱动解耦表征学习。在高层模块中,我们利用同一模态特征内属性因子间的差异性,以及不同模态间相同属性因子的一致性;而在低层模块中,则通过共享相同属性值的项目间内在关联进行表征学习。 

#### 3.2.1 高层属性驱动解耦表征学习。
为获得多模态特征中各属性因子的鲁棒独立表征,AD-DRL实现模态内特征解耦并确保跨模态表征一致性。下文将详述模态内解耦与跨模态解耦机制。 

**模态内解耦。** 在获得各模态的特征向量后,我们将这些向量分割为若干片段。然而,不同属性因子在片段内部仍然存在纠缠现象,即单个片段可能同时包含价格相关和品牌相关特征。

---

为此,我们采用属性分类器促使每个片段预测对应属性,从而实现各模态特征中属性因子的解耦,如图1所示。以商品文本嵌入的第k个片段 $v_{t}^{k}$ 为例, 

$$
\begin{cases}
z^{k}=W_{intra,t}v_{t}^{k}+b_{intra,t},\\
l_{intra,t}^{k}=-\Sigma_{n=1}^{K}\overline{z}_{n}^{k}log\frac{expz_{n}^{k}}{\sum_{m}exp~z_{m}^{k}}
\end{cases} \quad (3)
$$
其中 $W_{intra,t}\in\mathbb{R}^{K\times\frac{d}{K}}$ 和 $b_{intra,t}\in\mathbb{R}^{K}$ 分别表示分类器的权重矩阵和偏置向量, $z^{k}$ 为预测逻辑值, $z_{n}^{k}\in z^{k}.$ 代表文本块 $v_{t}^{k}$ 的真实标签(属性标注)。对于文本嵌入 $v_{t}$ 中的所有文本块,我们针对文本特征中的全部属性因子计算如下损失函数:

---

![](./images/AD-DRL/Figure1.png)

*(图1:我们提出的AD-DRL模型中高层与低层属性驱动的解耦表征学习模块。(a)模态内解耦模块利用同一模态特征中属性因子(如价格、品牌、类别和流行度)间的差异性;(b)模态间解耦模块利用不同模态特征中同一属性因子的一致性;(c)低层解耦表征学习模块则利用共享相同属性值(如不同级别的流行度值:超级流行、流行、中等、新兴和未知)的物品间内在关联。)* 

---

$$ 
l_{intra,t}=\sum_{k=1}^{K}l_{intra,t}^{k}\cdot \quad (4)
$$  

类似地,我们可以定义损失函数 $l_{intra,u},l_{intra,i}$ 和 $l_{intra,v}$,以促使各属性因子的特征分别集中在用户ID、物品ID和视觉特征对应的分块中。最终,模态内解耦的总损失函数表示为: [cite: 4]

$$ 
l_{intra}=l_{intra,u}+l_{intra,i}+l_{intra,t}+l_{intra,v}. \quad (5)
$$  

**跨模态解耦。** 除了对各模态分别解耦外,多模态特征解耦表示学习面临的核心挑战在于处理从多模态中解耦出的因子间的相互关系。直观而言,不同模态特征中同一属性因子的分块应保持一致。例如对于品牌属性,物品在视觉和文本特征中应具有相同的品牌信息。换言之,跨模态共享相同属性的分块应高度相似,而代表不同属性的跨模态分块则应显著不同。 

---

为实现多模态推荐中的鲁棒表征,我们通过确保跨模态间相同属性的一致性,解耦了不同模态特征中的属性因子。受此启发,如图1所示,我们设计了一种跨模态对比损失。具体而言,对于任意两种模态表征(如 $v_t$ 和 $v_v$),将同一属性因子片段(即 $(v_{t}^{k},v_{v}^{k})$)作为正样本对,不同属性片段( $\mathbb{P}(v_{t}^{k},v_{v}^{n\ne k}),(v_{t}^{n\ne k},v_{v}^{k}))$作为负样本对。随后,文本与视觉特征间的跨模态对比损失定义如下: 

$$
l_{t\leftrightarrow v} = \sum_{k=1}^{K} -log \frac{exp(v_t^k \cdot v_v^k / \tau)}{\sum_{n=1}^{K} exp(v_t^k \cdot v_v^n / \tau)} -log \frac{exp(v_t^k \cdot v_v^k / \tau)}{\sum_{n=1}^{K} exp(v_t^n \cdot v_v^k / \tau)} \quad (6)
$$ 



其中·表示点积, $\tau\in\mathbb{R}^{+}$ 为标量温度参数。通过将此对比学习约束应用于三种模态中任意两种,可实现跨模态特征解耦与对齐: 

$$
l_{inter}=l_{i\leftrightarrow t}+l_{i\leftrightarrow v}+l_{t\leftrightarrow v}. \quad (7)
$$ 

---

#### 3.2.2 低级属性驱动的解耦表征学习

每个属性对应一组可能取值 $(\tilde{y}_{1}^{k},\tilde{y}_{2}^{k},\cdot\cdot\cdot,\tilde{y}_{A_{k}}^{k})$，其中 $A_{K}$ 表示该属性的取值总数。

例如流行度属性可分为五级：超级流行、流行、一般、新兴和未知。通过挖掘同一因子下各属性值的关联关系，可增强表征解耦效果。因此我们鼓励基于属性的解耦表征能够预测物品的具体属性值，从而学习更具鲁棒性的表征。 

显然,单一模态可能无法包含特定属性的全部信息。例如,图像能更直观反映物品的颜色、品牌等属性,但难以直接体现价格或受欢迎程度等特征。因此,为全面准确刻画物品属性值,我们首先将所有模态的特征进行整合。通过多模态注意力机制,量化不同模态对各属性的关注权重。具体而言,对于k属性,其各模态注意力权重由双层神经网络计算得出: 

$$
\begin{cases}
\hat{a}^{k}=W_{a2}tanh(W_{a1}(v_{i}^{k}+v_{t}^{k}+v_{v}^{k})+b_{a})\\
a^{k}=Softmax(\hat{a}^{k}),
\end{cases} \quad (8)
$$


其中 $W_{a1}\in\mathbb{R}^{3\times\frac{d}{K}}$ 和 $W_{a2}\in\mathbb{R}^{3\times3}$ 分别是神经网络第一层和第二层对应的权重矩阵, $b_{a}$ 表示偏置向量,tanh为激活函数。采用Softmax将 $\hat{a}^{k}$ 归一化为概率分布。 

---

由此,我们得到该物品第k个属性因子的最终表征如下: 
$$ 
v_{y}^{k}=a_{i}^{k}\cdot v_{i}^{k}+a_{t}^{k}\cdot v_{t}^{k}+a_{v}^{k}\cdot v_{v}^{k}, \quad (9)
$$  
其中 $a_{i}^{k},a_{t}^{k},a_{v}^{k}\in a^{k}$ 分别表示物品ID嵌入、文本特征和视觉特征的注意力权重。

类似于第3.2.1节中模态内解缠的操作,我们旨在通过分类层(如图1所示)进行属性值预测,在低层级实现解耦表征学习: 

$$
\begin{cases}
y^{k}=W_{k}v_{y}^{k}+b_{k},\\
l_{low}^{k}=-\sum_{i=1}^{A_k}\overline{y}_{i}^{k}log\frac{exp~y_{i}^{k}}{\sum_{j}exp~y_{j}^{k}},\\
l_{low}=\sum_{k=1}^{K}l_{low}^{k}
\end{cases} \quad (10)
$$

其中 $W_{k}\in\mathbb{R}^{A_{K}\times\frac{d}{K}}$ 和 $b_{k}\in\mathbb{R}^{A_{k}};$ 分别为分类器的权重矩阵和偏置向量。我们通过以交叉熵损失形式定义的独立属性分类任务,监督每个属性子空间的训练。

---

### 3.3 偏好预测与模型学习

#### 3.3.1 偏好预测。
至此我们已探讨如何分别获取用户与物品的属性驱动解耦表征 $v_{u}=(v_{u}^{1},v_{u}^{2},\cdot\cdot\cdot,v_{u}^{K})$ 和 $v_{y}=(v_{y}^{1},v_{y}^{2},\cdot\cdot\cdot,v_{y}^{K})$。通过为每个解耦表征分配如上所述的属性,我们能够在属性层面预测用户偏好,从而增强模型的可解释性与可控性。具体而言,要估计用户对物品的偏好,关键在于考量其对物品各属性的偏好程度。为此,我们首先计算用户对物品每个属性的偏好得分,继而整合各属性得分以估算用户对该物品的整体偏好: 

$$
\begin{cases}
s_{u,i,k}=\sigma(v_{u}^{k}\cdot v_{y}^{k}),\\
s_{u,i}=\sum_{k=1}^{K}s_{u,i,k},
\end{cases} \quad (11)
$$

其中·表示点积, $\sigma(\cdot)$ 代表激活函数。本文采用softplus函数确保输出分数 $s_{u,i,k}$ 为正 $s_{u,i,k}$ 表示用户对物品 属性的偏好评分。 

---

#### 3.3.2 训练协议。
基于上述预测偏好分数,我们推荐与目标用户偏好匹配的Top-n排序物品列表。采用贝叶斯个性化排序(BPR)损失函数[31]优化模型参数, 

$$ 
\mathcal{L}_{BPR}=\sum_{(u,i_{+},i_{-})\in\mathcal{D}}-log~\phi(s_{u,i_{+}}-s_{u,i_{-}})+\lambda||\Theta||_{2}^{2}, \quad (12)
$$  

其中 $\lambda$ 是控制 $L_{2}$ 正则化的系数;D表示训练集; $i_+$ 和 $i_-$ 分别代表用户交互记录中已观测和未观测物品。总体而言,AD-DRL的总损失函数定义为: [cite: 8]

$$ 
\mathcal{L}=\mathcal{L}_{BPR}+\alpha\sum_{(u,i)\in\mathcal{D}}l_{intra}+\beta\sum_{(u,i)\in\mathcal{D}}l_{inter}+\gamma\sum_{(u,i)\in\mathcal{D}}l_{low}, \quad (13)
$$ 
其中 $\alpha,\beta$ 和 $\gamma$ 是控制三个解耦模块权重的超参数。 [cite: 8]

---

## 4 实验

### 4.1 实验设置

#### 4.1.1 数据集。
我们采用广泛使用的真实世界推荐数据集——亚马逊评论数据集[26]进行实验评估。除用户-物品交互数据外,该数据集还包含24个产品类别中物品的多模态信息(如评论和图像)及各类属性(如价格、品牌等)。评估选用该数据集中的三个产品类别:婴儿用品、玩具游戏和体育用品。参照[19]的方法,所有数据集均过滤冷门物品和非活跃用户,确保每个物品和用户至少拥有5条交互记录。三个数据集的基本统计信息如表1所示。 

在本研究中,除用户-物品交互数据和物品多模态信息外,还需利用物品的多个属性及其关联属性值来指导解耦表征学习过程。具体而言,我们采用四种典型属性(即价格、流行度2、品牌和类别),各属性值均基于亚马逊数据集提供的元数据编制:品牌与类别属性直接采用亚马逊提供的原始值; [cite: 8] 价格与流行度属性则参照CoHHN[44]的方法,根据数值大小离散化为五个等级。 

---

表1展示了实验中各属性的具体统计信息。值得注意的是,本方法可涵盖客观反映物品特征的广泛属性,远不止上述四种。



**表1:三个数据集的基本统计量。"#"表示统计值的数量。**

<style scoped>
.table1 {
  font-size: 0.75em;
  width: 100%;
  margin: 0 auto;
  table-layout: fixed;
}
.table1 th, .table1 td {
  padding: 4px 2px;
  text-align: center;
  overflow: hidden;
  word-wrap: break-word;
}
.table1 th:first-child, .table1 td:first-child {
  width: 12%;
  text-align: left;
  padding-left: 6px;
}
.table1 th:nth-child(2), .table1 td:nth-child(2),
.table1 th:nth-child(3), .table1 td:nth-child(3),
.table1 th:nth-child(4), .table1 td:nth-child(4) {
  width: 13%;
}
.table1 th:nth-child(5), .table1 td:nth-child(5) {
  width: 10%;
}
.table1 th:nth-child(6), .table1 td:nth-child(6),
.table1 th:nth-child(7), .table1 td:nth-child(7),
.table1 th:nth-child(8), .table1 td:nth-child(8),
.table1 th:nth-child(9), .table1 td:nth-child(9) {
  width: 11%;
}
</style>

<table class="table1">
<thead>
<tr>
<th>Dataset</th>
<th>#user</th>
<th>#item</th>
<th>#interaction</th>
<th>sparsity</th>
<th>#price</th>
<th>#popularity</th>
<th>#brand</th>
<th>#category</th>
</tr>
</thead>
<tbody>
<tr>
<td>Baby</td>
<td>12,637</td>
<td>18,646</td>
<td>121,651</td>
<td>99.95%</td>
<td>5</td>
<td>5</td>
<td>663</td>
<td>1</td>
</tr>
<tr>
<td>Toys Games</td>
<td>18,748</td>
<td>30,420</td>
<td>161,653</td>
<td>99.97%</td>
<td>5</td>
<td>5</td>
<td>1,288</td>
<td>19</td>
</tr>
<tr>
<td>Sports</td>
<td>21,400</td>
<td>36,224</td>
<td>209,944</td>
<td>99.97%</td>
<td>5</td>
<td>5</td>
<td>2,081</td>
<td>18</td>
</tr>
</tbody>
</table>

---

**表2：不同推荐方法在三个数据集上的性能对比。最佳结果以粗体标出。**
![](./images/AD-DRL/table2.png)


---
符号*表示基于双尾配对t检验,改进效果显著 $(p<0.05)$。

1.在所有数据集上,我们将AD-DRL及其变体的各因子嵌入维度固定为32,并采用Xavier初始化器[7]对模型参数进行初始化。 $\alpha, \beta, \gamma$ 和 $L_{2}$ 正则化系数在 {$\{1e^{-3}, 5e^{-3}, 1e^{-2},..., 5e^{+0}, 1e^{+0}, 1e^{+1}$ 范围内搜索,负样本数量在{2,4,8}中调优。此外,每5个epoch保存一次模型参数,采用早停策略[38]——若Recall@20指标连续50轮未提升则提前终止训练。我们公开了代码和数据集以促进实验复现3。 

---

#### 4.1.2 基线方法。
我们将本方法与最先进方法进行对比,包括基于协同过滤的方法(即NeuMF [12]、NGCF [38]和DGCF[39])和基于多模态协同过滤的方法(JRL[46]、MMGCN[42]、MAML [21]、GRCN[41]、DMRL[19]和BM3[47])。每类方法均包含一个解耦表征学习模型(即DGCF和DMRL)作为对照。除上述方法外,我们还构建了本模型的变体 $AD-DRL_{ID}$,该版本剔除了多模态信息的使用,从而确保与基于协同过滤模型的公平比较。 

#### 4.1.3 评估指标与参数设置。
针对每个数据集,我们将每位用户的交互记录按8:2比例随机划分为训练集和测试集。从训练集中随机选取10%的交互作为验证集用于超参数调优。采用Recall@n和NDCG@n作为准确性指标,在top-n推荐任务中评估性能(默认 $n=20$,旨在推荐前几个物品)。 [cite: 8]

采用Pytorch工具包[29]实现模型。为保证公平性,所有方法均使用Adam优化器[17],默认学习率为0.0001,批处理规模为1024。在所有数据集上,我们将AD-DRL及其变体的各因子嵌入维度固定为32,并采用Xavier初始化器[7]对模型参数进行初始化。

---

$\alpha, \beta, \gamma$ 和 $L_{2}$ 正则化系数在 $\{1e^{-3}, 5e^{-3}, 1e^{-2}, \cdots, 5e^{+0}, 1e^{+1}\}$ 范围内搜索,负样本数量在{2,4,8}中调优。此外,每5个epoch保存一次模型参数,采用早停策略[38]——若Recall@20指标连续50轮未提升则提前终止训练。我们公开了代码和数据集以促进实验复现³。

---

### 4.2 性能对比

我们将总体性能对比结果总结于表2。第一模块的方法仅使用用户-物品交互数据,而第二模块的方法则同时利用了文本、视觉信息及用户-物品交互数据。通过该表可得出以下观察结论: [cite: 12]

* NeuMF、NGCF、DGCF和AD-DRL ID是仅基于用户-物品交互数据训练的方法。其中,NeuMF凭借深度神经网络建模用户与物品间非线性交互的能力,优于传统基于矩阵分解的方法[12]。NGCF和DGCF通过利用高阶信息取得最优性能,而DGCF采用解耦表征捕捉多样化用户意图的表现优于NGCF,这验证了其在增强用户与物品表征鲁棒性方面的有效性。此外,虽然DGCF和AD-DRL ID均采用解耦表征学习,但AD-DRLID的性能显著优于DGCF。这凸显了我们提出的属性驱动解耦表征学习方法的优越性。 [cite: 12]

---

* 总体而言,多模态推荐方法的表现优于仅使用用户-物品交互的方法,这证明了利用多模态信息学习用户和物品表征的有效性。虽然简单的神经网络结构导致JRL的性能低于基于图的方法(NDCG和DGCF),但通过整合丰富的多模态信息使其超越了NeuMF。MMGCN通过利用用户-物品交互来指导不同模态的表征学习,在所有数据集上均优于NGCF。MAML通过采用物品的多模态特征建模用户多样化偏好,其表现优于NGCF。GRCN通过利用模态特征发现并修剪用户-物品交互图中的潜在假阳性边,性能超越了MAML。DMRL通过捕捉不同模态特征对各解耦因子的差异化贡献,取得了更优的结果。 [cite: 12]
* 此外,我们提出的AD-DRL模型在三个数据集上均以显著优势持续超越所有基线方法。这归功于以下两方面的协同作用:首先,通过利用不同层级的属性来指导解耦表征学习过程,能够获得鲁棒的用户和项目表征;其次,在表征学习中融入额外属性信息有助于缓解推荐系统中的数据稀疏性问题。 

---

### 4.3 解耦表征的可视化分析

AD-DRL在高低两种属性粒度层级上对用户和项目表征进行解耦学习。为验证其有效性,我们采用t-SNE[36]技术对Sports数据集中各粒度层级的解耦向量进行聚类可视化。 

---

#### 4.3.1 高层属性驱动的解耦分析。
图2展示了各模态特征经高层属性驱动解耦模块处理后的解耦向量,同色圆点表示对应同一属性的向量。可见我们的模型能有效区分用户和项目在不同模态下对应各属性的表征向量,证明了模型在属性层级解耦的有效性。此类解耦表征能更精准捕捉用户偏好和项目在不同属性维度的特征。


---

![bg fit left](./images/AD-DRL/Figure2.png)


---
![bg fit left](./images/AD-DRL/Figure3.png)



---
![bg fit left](./images/AD-DRL/Figure4.png)



---

#### 4.3.2 低层属性驱动的解耦验证。
为验证低层(属性值层级)解耦的有效性,我们在图3中可视化各属性因子的表征(即方程9中的 $v_{y}^{k}$ )。不同色点代表不同属性值4。可见AD-DRL学习到的不同属性值表征分离良好,同属性值表征高度聚集。这些结果表明AD-DRL能在属性值层面实现更细粒度解耦,从而更精准捕捉用户偏好。 

4针对品牌和类别属性,由于数据集中属性值过多,难以在图3中全部展示。因此对于这两个属性,我们仅选取数据集中对应商品数量最多的前5个属性值进行演示。 

---

### 4.4 可解释性研究

为深入理解模型的可解释性,本节在图4中提供了若干定性分析案例。以体育数据集为例,我们随机选取两名购买相同商品(i2099和i3460)的用户(u18629和 u6805)进行说明。由于我们已基于属性对用户和商品表征进行解耦,可根据公式11分析各属性对推荐结果的贡献度,这显著提升了模型的可解释性。例如,价格因素对交互 (u18629,i2099)的影响最大,表明u18629因价格偏好i2099;而u6805更可能因品牌偏好购买i2099。该观察表明不同用户对同一商品存在差异化偏好。此外还可发现用户偏好具有一致性,例如u6805更重视商品品牌而非流行度或价格,这与u18629形成鲜明对比。 

---

### 4.5 可控性研究

本节通过操纵用户对特定属性的偏好来评估AD-DRL的可控性。具体而言,我们改变方程11中用户对特定属性的偏好评分,以评估这种改变是否会产生推荐结果的预期变化。对方程11中的公式修改如下: 
$$ 
s_{u,i} = \xi*s_{u,i,a}+\sum_{k\ne a}s_{u,i,k} \quad (14)
$$  

其中 $\xi$ 表示 $s_{u,i,a}$ 的缩放因子,用于调节属性a对 $s_{u,i}$ 的影响。针对特定属性(本节选择价格和流行度作为研究对象),我们系统地将 $\xi$ 取值调整为2、1、0.5、0和-1。通过该过程可精细观察推荐输出的变化趋势,结果如图5所示。 [cite: 15]

我们以价格属性为例进行详细分析。如图5a所示,我们从Baby数据集选取了100名基于购买记录始终选购低价商品的用户。图5a中不同颜色代表不同价格层级,展示了AD-DRL算法在给定 $\xi$ 值时,各层级推荐商品的分布情况。 

通过比较 

---

![bg fit left](./images/AD-DRL/Figure5.png)



---

通过观察AD-DRL在不同 $\xi$ 值下的推荐表现,我们得出以下结论: 

* 当 $\xi=1$ 时(即采用原始AD-DRL算法),可见该方法能有效捕捉用户对低价商品的偏好,并为其推荐更多经济实惠的商品。
* 当 $\xi=2$ 时(即提高价格因素对用户偏好评分的影响),可观察到AD-DRL会倾向于向这些用户推荐更多低价商品。 
* 当 $\xi=0.5$ 或0时(意味着我们降低甚至消除价格对用户偏好分数的影响),可发现AD-DRL推荐的物品在价格维度上更具多样性。值得注意的是,即使 $\xi=0$ 时,AD-DRL推荐的物品仍倾向于低价。这可能归因于品牌关联的保留模块,且品牌与价格存在相关性。例如,某些高端品牌提供溢价产品,而其他品牌则提供更实惠的选择。 
* 更有趣的是,当 $\xi=-1$ 时(即让AD-DRL推荐与用户价格偏好相反的物品),可以观察到AD-DRL确实会推荐部分更高价的商品。
---

上述结果表明,通过调整 $\xi$ 值可使AD-DRL的推荐结果符合预期,由此证明该算法的可控性。在另一组偏好高价商品的用户群体中也观察到了类似现象。

---

**表3:我们提出的AD-DRL方法在三个数据集上的消融实验结果,最佳结果以粗体标出。**

| Datasets                | Baby          |             | Toys Games    |             | Sports        |             |
|-------------------------|---------------|-------------|---------------|-------------|---------------|-------------|
| Metrics                 | Recall        | NDCG        | Recall        | NDCG        | Recall        | NDCG        |
| AD-DRL w/o disentangling| 0.0888        | 0.0550      | 0.1452        | 0.1375      | 0.1152        | 0.0741      |
| AD-DRL w/o intra        | 0.0925        | 0.0567      | 0.1492        | 0.1397      | 0.1164        | 0.0750      |
| AD-DRL w/o inter        | 0.0959        | 0.0585      | 0.1517        | 0.1429      | 0.1196        | 0.0754      |
| AD-DRL w/o high         | 0.0903        | 0.0564      | 0.1492        | 0.1391      | 0.1160        | 0.0748      |
| AD-DRL w/o low          | 0.0916        | 0.0553      | 0.1492        | 0.1394      | 0.1177        | 0.0755      |
| AD-DRL                  | **0.0968** | **0.0588** | **0.1524** | **0.1435** | **0.1200** | **0.0756** |

---

### 4.6 消融研究

为验证AD-DRL中关键组件的作用,我们设置了以下模型变体:1) AD-DRL $w/o$ 解耦:训练过程中不进行解耦表示学习;2) AD-DRL $w/o$ 模态内:仅移除模态内解耦模块;3) AD-DRL $w/o$ 模态间:仅移除模态间解耦模块;4)AD-DRL $w/o~high$:移除高层属性驱动的解耦表示学习模块; 5) AD-DRL $w/o$ 低层:移除低层属性驱动的解耦表示学习模块。 

表3展示了所有变体的实验结果,从中我们得出以下观察结论:

* 独立使用高层或低层解耦表征学习均可显著提升性能,这表明模态内与模态间的解耦表征学习均具有积极作用。 
* AD-DRL融合了高层与低层解耦表征学习模块,相比设计的两个变体取得了显著性能提升,验证了属性与属性值层级联合建模的必要性与合理性。 
* 深入分析高层解耦表征学习模块发现:移除模态内解耦模块比移除模态间解耦模块导致更显著的性能下降,说明模态内解耦是表征学习的根基,而模态间解耦能进一步提升模型性能。 

---

## 5 结论

本文揭示了推荐系统中现有解耦表征学习技术的局限性。当前方法以无监督方式解耦用户-物品交互背后的潜在因素,导致可解释性和可控性不足。为突破这一局限,我们提出一种属性驱动的解耦表征学习方法 (AD-DRL),能在多模态特征中解耦属性因子。具体而言,AD-DRL实现单模态特征内的解耦,并确保跨模态表征在属性层面的一致性。此外,该方法利用共享相同属性值的物品间内在关联。实验结果表明AD-DRL的优越性,并验证了其在可解释性与可控性方面的能力。 

