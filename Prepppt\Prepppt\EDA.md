---
marp: true
---

# **K-means 聚类分析报告**

## **1. 引言**

本报告旨在通过 K-means 聚类分析，对学生的成绩及学习行为数据进行分群，以发现不同学生群体的特征差异。通过聚类分析，教育管理者可以获得数据驱动的洞见，优化教学策略，提升教育质量。

---

## **2. 数据准备**

### **2.1 数据来源与合并**

本次分析使用了两份学生成绩数据集：数学课程 (`student-mat.csv`) 和葡萄牙语课程 (`student-por.csv`)。为了综合分析学生在不同课程中的表现，依据多个共同变量（如学校、性别、年龄、家庭背景等）对两份数据进行了合并，形成了综合数据集 `d3`。

---
### **2.2 描述性统计分析**

对合并后的数学成绩 (`G3.x`) 进行了描述性统计分析，以了解其基本分布情况。

| 统计量       | 数值  |
|--------------|-------|
| 最小值       | 0.00  |
| 第一四分位数 | 8.00  |
| 中位数       | 11.00 |
| 均值         | 10.39 |
| 第三四分位数 | 14.00 |
| 最大值       | 20.00 |

**解释**：`G3.x` 的成绩分布范围从0到20，大部分学生的成绩集中在8到14之间，均值为10.39，显示出一定的成绩波动。

---

### **2.3 数据分布可视化**

通过直方图、密度图和箱线图对 `G3.x` 的分布进行了可视化分析。

- **直方图**显示成绩大多数集中在8到14之间，呈现右偏分布。
- **密度图**进一步确认了成绩的分布形态，并通过数据点的密集区域展示了成绩分布的集中趋势。
- **箱线图**揭示了存在若干异常值，表明部分学生的成绩显著高于或低于其他学生。

---

## **3. 聚类分析**

### **3.1 变量选择与数据标准化**

为进行聚类分析，选择了以下与学生成绩及学习行为相关的数值型变量：

- 数学成绩：`G1.x`、`G2.x`、`G3.x`
- 个人特征：`age`（年龄）
- 学习行为：`studytime.x`（学习时间）、`failures.x`（失败次数）、`absences.x`（缺勤次数）

在进行聚类分析前，对选定的数值型变量进行了标准化处理，以消除不同量纲对聚类结果的影响。

---

### **3.2 确定最佳聚类数**

为了确定最适合的数据聚类数目，采用了轮廓系数（Silhouette）方法。通过分析不同聚类数下的轮廓系数，发现当聚类数 `k=2` 时，轮廓系数达到峰值，表明将数据分为两个簇具有最佳的分离效果。

**最佳聚类数**：2

---

### **3.3 K-means 聚类结果**

基于最佳聚类数 `k=2`，执行了 K-means 聚类分析，结果如下：

#### **3.3.1 聚类中心**

| 簇别 | G1.x  | G2.x  | G3.x  | 年龄   | 学习时间 | 失败次数 | 缺勤次数 |
|------|-------|-------|-------|--------|----------|----------|----------|
| 1    | 0.739 | 0.726 | 0.696 | -0.183 | 0.130    | -0.351   | -0.142   |
| 2    | -0.829| -0.815| -0.781| 0.206  | -0.145   | 0.394    | 0.159    |

#### **3.3.2 聚类规模**

| 簇别 | 样本数量 |
|------|----------|
| 1    | 202      |
| 2    | 180      |

**解释**：

- **簇1**：该簇的学生在数学成绩（`G1.x`、`G2.x`、`G3.x`）上表现较好，学习时间适中，失败次数和缺勤次数较少。年龄分布较小，表明该群体可能由年轻且成绩优异的学生组成。
  
- **簇2**：该簇的学生在数学成绩上表现较差，学习时间较少，失败次数和缺勤次数较多。年龄分布较大，表明该群体可能包括年龄较大的学生且学业表现欠佳。

---

## **4. 聚类结果可视化**

### **4.1 主成分分析（PCA）降维**

为了更直观地展示聚类结果，采用主成分分析（PCA）将标准化后的数据降维至二维空间。通过绘制 PCA 散点图，不同簇别的学生在主成分空间中的分布情况得以清晰呈现。

---
![w:600 h:400 ](./images/Rplot_kmeans_result.png)

**图1：K-means 聚类结果的 PCA 散点图**

*图中不同颜色代表不同的聚类簇，显示出两个簇在主成分空间中的明显分离。*

---
### **4.2 聚类中心特征解释**

通过分析聚类中心，可以深入理解每个簇的特征：

- **簇1**：
  - **成绩**：各项成绩指标均高于平均水平。
  - **学习行为**：适中的学习时间，较低的失败和缺勤次数。
  - **年龄**：整体年龄偏小。
  
- **簇2**：
  - **成绩**：各项成绩指标均低于平均水平。
  - **学习行为**：较少的学习时间，较高的失败和缺勤次数。
  - **年龄**：整体年龄偏大。

---

## **5. 结果总结**

### **5.1 数据适配性**

通过数据的选择、合并与标准化处理，确保了数据的质量和适配性，为后续的聚类分析奠定了坚实的基础。

### **5.2 聚类分析结果**

- **最佳聚类数**：通过轮廓系数方法确定最佳聚类数为 **2**。
- **聚类分布**：数据被有效分为两个簇，分别代表成绩优异和成绩欠佳的学生群体。
- **簇特征**：
  - **簇1**：成绩优秀，学习行为良好，缺勤和失败次数少，年龄偏小。
  - **簇2**：成绩较差，学习行为欠佳，缺勤和失败次数多，年龄偏大。

---
### **5.3 可视化分析**

通过 PCA 降维后的散点图，清晰地展示了两个簇在主成分空间中的分布，进一步验证了聚类结果的有效性。

---

## **6. 结论与建议**

### **6.1 结论**

本次 K-means 聚类分析将学生分为两类，分别对应成绩优秀和成绩较差的群体。聚类结果揭示了学生成绩与学习时间、失败次数、缺勤次数之间的显著关联，表明这些因素在学生学业表现中起着重要作用。

---
### **6.2 建议**

基于聚类分析结果，提出以下针对性建议：

1. **针对成绩较差的学生（簇2）**：
   - **增加学习时间**：鼓励学生投入更多时间进行学习，提高学业成绩。
   - **减少缺勤次数**：通过监控和管理缺勤情况，确保学生按时上课。
   - **降低失败次数**：提供辅导和支持，帮助学生克服学业困难，减少课程失败。
   
2. **针对成绩优秀的学生（簇1）**：
   - **保持良好学习习惯**：继续鼓励学生保持现有的学习时间和出勤率，进一步提升学业成绩。
   - **提供进阶学习机会**：为成绩优秀的学生提供更具挑战性的学习内容，促进其全面发展。
---

**附录**

### **附录A：聚类中心表**

| 簇别 | G1.x  | G2.x  | G3.x  | 年龄   | 学习时间 | 失败次数 | 缺勤次数 |
|------|-------|-------|-------|--------|----------|----------|----------|
| 1    | 0.739 | 0.726 | 0.696 | -0.183 | 0.130    | -0.351   | -0.142   |
| 2    | -0.829| -0.815| -0.781| 0.206  | -0.145   | 0.394    | 0.159    |

### **附录B：聚类规模表**

| 簇别 | 样本数量 |
|------|----------|
| 1    | 202      |
| 2    | 180      |


