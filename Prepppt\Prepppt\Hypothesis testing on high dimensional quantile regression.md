---
marp: true
theme: suibeppt
paginate: false
style: |
  section {
    font-size: em;
  }

---
<!-- backgroundImage: url(./images/suibe.png) -->
<!-- _header:  -->
# Hypothesis testing on high dimensional quantile regression
# 高维分位数回归的假设检验

---

## 1.引言
### 1.1 研究动机
- 本文以中国股市的沪深 300 指数及其对应的上市开放式基金 (LOF) 为研究对象，试图通过研究经典的因子模型，包括资本资产定价模型 (Sharpe，1964) 和三因子模型 (Fama and French，1993) 来验证中国股市是否满足有效市场假说 (EMH，Fama，1970)
- 数据集包含 97 只股票及其 132 条周收益。尽管该指数由业绩稳定的蓝筹股组成，但收益序列仍然表现出非平稳、高波动、重尾和非对称特征
- 鉴于 132 个条目和 97 个潜在解释变量，还面临着一个高维问题，无法通过传统的检验统计量进行可靠的分析。因此，我们需要构建一种适用于高维数据的统计推断方法，该方法对异构和重尾数据具有鲁棒性。这就让作者想到了使用分位数回归方法来分析问题，通过分位数回归对高维变量进行显著性检验。

---

### 1.2 前人研究
#### 1.2.1 Wald型检验、分位数似然比检验
- 当解释变量的维数𝑝固定且远小于样本容量𝑛时，Koenker and Machado（1999）及 Koenker（2005）提出可以使用Wald型检验、分位数似然比检验和秩得分检验。
- 但当𝑝接近甚至超过𝑛时，这些检验方法效果较差。其原因是在高维环境中，Fisher信息矩阵会恶化，导致在备择假设下无法获得不一致的最大似然估计量。另外，Wald型检验和分位数似然比检验的极限分布涉及非参数密度估计，这会给理论分析和实际应用带来困难。

#### 1.2.2 秩得分检验
- 样本分位数的秩得分检验（Hájek and Šidák，1967）已被Gutenbrunner 和 Jurečková（1992）、Gutenbrunner 等（1993）、Koenker 和 Machado（1999）、Wang 和 He（2007）以及 Wang 等（2009）扩展到回归分位数。检验统计量基于分位数得分函数和精度矩阵建立。
- 精度矩阵是保持秩得分检验统计量尺度不变所必需的，但在高维问题中，协方差矩阵不可逆。涉及精度矩阵的检验统计量仅适用于固定和低维问题，而不适用于高维问题。

---

#### 1.2.3 bootstrap方法
- 还有另外一种基于 bootstrap 方法的高维分位数回归检验（Tang et al，2020）。Bootstrap 方法不依赖于数据的特定分布假设，适用于各种类型的数据集。
- 该检验统计量通常没有明确的抽样分布或渐近分布的参数不易估计。因此，需要通过 bootstrap 方法确定检验的临界值，这可能会影响统计和计算效率。另外，基于 bootstrap 的方法通常是通过最大范数构造的，因此在捕获强信号方面具有一定优势，但对多种弱信号的识别能力略显不足。

---

### 1.3 提出本文方法
- 考虑高维设置，其中参数空间维数与样本大小的比率趋向于一个有限常数，形式为𝑝∕𝑛 → 𝑐 ∈ (0, ∞)，本文通过构造基于得分函数的U统计量提出一种新的检验统计量。新的检验统计量同时求解不可逆协方差矩阵和非参数密度估计。
- 然后，在零假设和局部替代假设下推导出所提统计量的渐近正态性。 与传统的分位数回归统计推断需要估计误差分布的密度不同，我们只需要估计$\text{tr}(\Sigma^2)$，其中$\Sigma$是协变量的协方差矩阵。
- 最后，通过数值模拟研究来验证我们提出的方法是否适用于高维环境，以及是否对重尾分布具有鲁棒性。与一些现有的方法相比，新方法可以有效地控制I类错误率，并且对重尾数据具有较高的经验功效。通过在不同分位数下检验模型显著性，我们通过多因子定价模型研究了中国股市在古典有效市场假说下的有效性。

### 1.4 创新点
- 本文考虑了部分分位数回归检验。相关文献已经对构造基于得分函数的U统计量进行了很好的研究（Zhong and Chen，2011；Zheng et al，2020）。Zhang et al（2018）研究了高维分位数回归中保证渐近正态性的条件分位数依赖性检验。然而，他们没有考虑本文考虑的部分分位数回归检验。

---

## 2.研究方法

- 设 $y$ 是响应变量，$x_i$ 是 $p$ 维的协变量。分位数回归（Quantile Regression, QR，Koenker 和 Bassett, 1978）假设在给定 $x$ 的条件下，$y$ 的第 $\tau$ 个分位数函数满足如下关系：
  $$
  Q_{\tau}(y|x) = \alpha_{\tau} + x^T \beta_{\tau}\tag{1}
  $$
  其中 $(\alpha_{\tau}, \beta_{\tau}^T)^T$ 被称为分位数回归的分位数，适用于 $0 < \tau < 1$。在给定独立同分布（i.i.d.）样本 $(y_i, x_i), i = 1, \dots, n$ 的情况下，考虑如下线性模型：

  $$
  y_i = \alpha_{\tau} + x_i^T \beta_{\tau} + \varepsilon_i\tag{2}
  $$
  其中误差项 $\varepsilon_i$ 与 $x_i$ 独立，并且具有密度 $f_{\varepsilon}(\cdot)$，满足：$P(\varepsilon_i > 0) = 1 - \tau
 $
  
---

- 在本文中，需要检验的是在高维设置下检验如下假设：

  $$
  H_0: \beta_{\tau} = \beta_{\tau, 0} \quad \text{vs} \quad H_1: \beta_{\tau} \neq \beta_{\tau, 0}\tag{3}
  $$

  其中 $\beta_{\tau, 0}$ 是一个指定的 $p$ 维向量，且维度 $p$ 可能非常接近甚至大于样本量 $n$。在不失一般性的情况下，可以设： $
  \beta_{\tau, 0} = 0
  $

- 一般来说，当 $p$ 远小于 $n$ 时，对于给定的 $\tau \in (0, 1)$，模型 (1) 的估计量 $(\hat{\alpha}_{\tau}, \hat{\beta}_{\tau})$ 是通过最小化以下损失函数得到的：

  $$
  (\hat{\alpha}_{\tau}, \hat{\beta}_{\tau}) = \arg \min_{\alpha, \beta} L_{\tau}(\alpha, \beta) \triangleq \arg \min_{\alpha, \beta} \sum_{i=1}^n \rho_{\tau} (y_i - \alpha - x_i^T \beta)\tag{4}
  $$
  其中：$\rho_{\tau}(u) = u (\tau - I\{u < 0\})$，$I\{x \in A\}是一个指示函数，当 x \in A 时取值为 1，否则取值为 0。$
  
---

- 由于非参数密度估计涉及到分位数回归渐近分布中的应用，Wald 型统计量的实际应用和理论分析面临着挑战。因此，当维度 $p$ 较低时，提出了分位数回归秩得分检验以解决推断问题。

  使用分位数得分函数 $\phi(t) = \tau - I\{t < \tau\}$，秩得分检验统计量定义为：

  $$
  T_n^{RS} = \Omega_n^T S_n^{-1} \Omega_n / (\tau (1 - \tau)) \tag{5}
  $$

  其中 $S_n$ 是 $x$ 的样本协方差矩阵，$\Omega_n = n^{-1/2} \sum_{i=1}^n (x_i - \bar{x}) \hat{b}_{ni}$，且

  $$
  \hat{b}_{ni} = 
  \begin{cases} 
  \tau & \text{if } y_i > x_i^T \hat{\beta}_{\tau} \\
  \in (\tau - 1, \tau) & \text{if } y_i = x_i^T \hat{\beta}_{\tau} \\
  \tau - 1 & \text{if } y_i < x_i^T \hat{\beta}_{\tau}
  \end{cases} 
  $$

  $\hat{b}_{ni}$ 近似为在 $x_i^T \hat{\beta}_{\tau}$ 处评估的得分函数，并且被用于构造用于分位数回归的基于秩的检验（Koenker 和 Machado, 1999; Wang 等, 2009）。

- 然而，在高维场景下，当维度 $p$ 接近甚至大于样本量 $n$ 时，样本协方差矩阵的逆可能不存在，或者与真实值存在显著偏差。在这种情况下，秩得分检验可能不再可行，且需要重构适用于高维场景的新检验统计量。

---

### 2.1 新检验方法

- 在本节中作者基于得分函数构造了一种新的检验统计量。以下是分位数回归的估计函数对应于沿方向    $\beta$ 的分位数损失的方向导数，可以表示为如下形式：

  $$
  S_{\tau}(\alpha, \beta) = \sum_{i=1}^n x_i \psi_{\tau, i} (\alpha, \beta), \tag{6}
  $$

  其中 $\psi_{\tau, i} (\alpha, \beta) = I\{y_i - \alpha - x_i^T \beta < 0\} - \tau$。

  在原假设 $H_0: \beta_{\tau} = 0$ 下，$\mathbb{E}_{H_0}[\psi_{\tau, i} (\alpha, 0)] = 0$。令 $\psi_{\tau, i} = \psi_{\tau, i} (\alpha_{\tau}, 0) = I\{y_i < \alpha_{\tau}\} - \tau$，其中 $\hat{\psi}_{\tau, i} = \psi_{\tau, i} (\hat{\alpha}_{\tau}, 0) = I\{y_i < \hat{\alpha}_{\tau}\} - \tau$，$\hat{\alpha}_{\tau}$ 是原假设下 $(y_i, i = 1, \dots, n)$ 的 $\tau$ 分位数的估计量。

---

- $\sum_{i \neq j} x_i^T x_j \hat{\psi}_{\tau, i} \hat{\psi}_{\tau, j}$ 可以被构造成$S_{\tau}(\alpha, \beta)$的U型统计量。为了消除非零效应，将 $x_{ij} = x_i x_j$ 替换为$
\tilde{x}_{ij} = (x_i - \bar{x})^T (x_j - \bar{x}) + ||x_i - x_j||^2 / (2n)$

  最终得到新的检验统计量为：

  $$
  T_{n, p} = \frac{1}{2} \binom{n}{2}^{-1} \sum_{i \neq j} \tilde{x}_{ij} \hat{\psi}_{\tau, i} \hat{\psi}_{\tau, j}, \tag{7}
  $$

  其中 $\hat{\psi}_{\tau, i, j} = \hat{\psi}_{\tau, i} \hat{\psi}_{\tau, j}$ 是通过代入截距 $\hat{\alpha}_{\tau}$ 的估计量得到的。

  由于求和中的每一项包含样本均值，因此新的统计量不是标准的 U-统计量。然而，样本均值只是每个变量 $X_j, j = 1, \dots, p$ 在方差意义上的高阶项。因此将其称为类 U-统计量。期望该统计量的表现与 U-统计量相似。

  这避免了在新构造统计量的渐近分布中进行密度估计。
---

### 2.2 部分分位数回归检验

- 在本节中，本文将模型 (2) 中的协变量分为两组：

  $$
  y_i = \alpha_{\tau} + x_{1i}^T \beta_{\tau, 1} + x_{2i}^T \beta_{\tau, 2} + \varepsilon_i\tag{8}
  $$

  其中 $x_{1i}$ 和 $x_{2i}$ 分别是 $p_1$ 和 $p_2$ 维的变量，并且 $p_1 \ll p_2$，$p_1 + p_2 = p$。在高维模型中，只有少数协变量在 $x$ 中被认为是显著的，剩余的为冗余变量。因此，系数向量 $\beta_{\tau}$ 是稀疏的，且 $\beta_{\tau}$ 中非零分量的个数为 $s = o(n)$。

- 记 $x_{1i}$ 为重要变量的部分，这些变量通过先验信息、专家知识、变量选择、特征筛选等降维方法确定。因此，本文假设维度 $p_1$ 是固定且有限的，其假设为

  $$
  H_0 : \beta_{\tau, 2} = 0 \quad \text{vs} \quad H_1 : \beta_{\tau, 2} \neq 0. \tag{9}
  $$

- 假设 $\hat{\alpha}_{\tau}$ 和 $\hat{\beta}_{\tau, 1}$ 是零假设下模型中的 $\alpha_{\tau}$ 和 $\beta_1$ 的估计量，其中分位数回归模型中仅涉及 $x_1$，且 $\hat{\beta}_{\tau} = (\hat{\beta}_{\tau, 1}^T, 0^T)^T$。类似于公式 (7)，本文构造用于检验 (9) 的统计量为

  $$
  T_{1n, p} = \frac{1}{2} \binom{n}{2}^{-1} \sum_{i \neq j} \tilde{x}_{2ij} \hat{\psi}_{\tau, i}^{(1)} \hat{\psi}_{\tau, j}^{(1)}, \tag{10}
  $$

  其中 $\tilde{x}_{ij} = (x_{2i} - \bar{x}_2)^T (x_{2j} - \bar{x}_2) + ||x_{2i} - x_{2j}||^2 / (2n)$，$\bar{x}_2 = n^{-1} \sum_{i=1}^n x_{2i}$，$\hat{\psi}_{\tau, i, j}^{(1)} = \hat{\psi}_{1\tau, i} \hat{\psi}_{1\tau, j}$，且 $\hat{\psi}_{1\tau, i} = \psi_{\tau, i} (\hat{\alpha}_{\tau}, \hat{\beta}_{\tau}) = I\{y_i - \hat{\alpha}_{\tau} - x_{1i}^T \hat{\beta}_{\tau, 1} < 0\} - \tau$。

---

## 3. 渐进分布

为了推导统计量 $T_{n,p}$ 的渐近分布，本文首先提出了一些假设条件。

- **(C.1)** 误差项 $\varepsilon$ 的密度 $f_{\varepsilon}(t)$ 为正且远离零。此外，$f_{\varepsilon}(t)$ 可微且导数有界。

- **(C.2)** 维度 $p \to \infty$ 是允许的，同时样本量 $n \to \infty$。记 $\Sigma$ 为协变量 $x$ 的协方差矩阵。$\Sigma$ 是正定矩阵，且满足 $\text{tr}(\Sigma^4) = o(\text{tr}^2(\Sigma^2))$。

- **(C.3)** 回归系数向量 $\beta$ 满足 $\beta^T \Sigma \beta = o(1)$，$\beta^T \Sigma^3 \beta = o(n^{-1}\text{tr}(\Sigma^2))$，且 $\mathbb{E}[x^T \tau(x)] \Sigma \mathbb{E}[x \tau(x)] = o(n^{-1} \text{tr}(\Sigma^2))$，其中 $\tau(x) = \mathbb{E}(I\{y < \alpha_{\tau}\}|x) - \tau$。

- **(C.4)** 假设协变量向量 $x$ 可以表示为 $x = \Gamma z + \mu$，其中 $z = (Z_1, \dots, Z_m)^T$ 由 $m \geq p$ 个 i.i.d. 随机变量组成，且满足 $\mathbb{E}z = 0$ 和 $\text{Var}(z) = I_m$。$\mu$ 是一个 $p$ 维确定性向量，$\Gamma$ 是一个 $p \times m$ 的矩阵，且满足 $\Gamma \Gamma^T = \Sigma$。进一步假设 $\mathbb{E}Z_l^3 = 0$ 和 $\mathbb{E}Z_l^4 = 3 + \Delta < \infty$，$l = 1, \dots, m$。

---

**定理 1** 如果条件 (C.1)-(C.4) 成立，并且 $|\hat{\alpha}_{\tau} - \alpha_{\tau}| = O_p(n^{-1/2})$，则有：

(a) 在原假设 $H_0$ 下，

$$
\frac{n T_{n, p}}{\tau(1 - \tau) \sqrt{2 \text{tr}(\Sigma^2)}} \overset{L}{\to} N(0, 1); 
$$

(b) 在备择假设 $H_1$ 下，

$$
\frac{n (T_{n, p} - \mu_{n, p})}{\tau(1 - \tau) \sqrt{2 \text{tr}(\Sigma^2)}} \overset{L}{\to} N(0, 1), 
$$

其中$
\mu_{n,p} = \frac{(n-1)(n-2)}{n^2} \lVert \mathbb{E} x_i \tau(x_i) \rVert^2 - \frac{n-2}{n^2} \left( \bar{\tau} \mathbb{E} x_i^T x_i \tau(x_i) - \bar{\tau}^2 \text{tr}(\Sigma) \right)
$


渐近零分布可用于构造 $T_{n, p}$ 和 $T_{1n, p}$ 的拒绝域。在备择假设 $H_1$ 下的渐近分布展示了所提出检验的功效。当 $n$ 充分大时，$
\left| \bar{\tau} \mathbb{E} \tau(x_i) x_i^T x_i - \bar{\tau}^2 \text{tr}(\Sigma) \right| = o\left( \sqrt{\text{tr}(\Sigma^2)} \right)$，因此 $\mu_{n,p}$ 的第二项可以忽略。

---

- 根据定理 1，所提出检验统计量的功效函数为

  $$
  \Omega_n(\beta) = \Phi \left( -z_{\alpha} + \frac{n \lVert \mathbb{E} x_i \tau(x_i) \rVert^2}{\tau(1 - \tau) \sqrt{2 \text{tr}(\Sigma^2)}} \right) \left\{ 1 + o(1) \right\}, 
  $$

  其中 $\Phi(\cdot)$ 和 $z_{\alpha}$ 分别是标准正态分布的累积分布函数和上 $\alpha$ 分位点。

- 下面对 $d_n$ 作出定义：
  $$
  d_n \triangleq \frac{n \lVert \mathbb{E} x_i \tau(x_i) \rVert^2}{\tau(1 - \tau) \sqrt{2 \text{tr}(\Sigma^2)}}
  $$

  $d_n$衡量了 $H_0$ 和 $H_1$ 之间的距离。特别地，当 $\Sigma = I_p$ 时，有
  $$
  d_n = \frac{c \lVert \mathbb{E} x_i \tau(x_i) \rVert^2}{\tau(1-\tau)} n^{1/2} \{ 1 + o(1) \}, \quad c = \lim_{n \to \infty} p/n.
  $$

---

- 接下来，本文研究用于部分模型显著性检验的统计量 (10) 的渐近性质。为了适应这个特定的检验场景，需要调整定理 1 中使用的一些条件。令

  $$
  \hat{\theta}_{\tau} = (\hat{\alpha}_{\tau}, \hat{\beta}_{\tau, 1}^T)^T, \quad t = (t_0, t_1^T)^T, \quad t_1 = (t_{11}^T, 0^T)^T,
  $$

  其中 $t_{11}$ 表示一个 $p_1$ 维的向量。此外，令 $\tilde{\theta}_{\tau} = (\tilde{\alpha}_{\tau}, \tilde{\beta}_{\tau, 1}^T)^T$，其中 $\tilde{\theta}_{\tau}$ 是涉及 $x_1$ 的分位数回归模型中 check-loss 的最小化估计量。

- **(C.5)** 允许维度 $p_2 \to \infty$，样本量 $n \to \infty$。记 $\Sigma_2$ 为 $x_2$ 的协方差矩阵，$\Sigma_2$ 是正定的并满足 $\text{tr}(\Sigma_2^4) = o(\text{tr}^2(\Sigma_2^2))$。

- **(C.6)** 部分回归系数向量 $\beta_2$ 满足 $\beta_2^T \Sigma_2 \beta_2 = o(1)$, $\beta_2^T \Sigma_2^3 \beta_2 = o(n^{-1}\text{tr}(\Sigma_2^2))$，$\mathbb{E} [x_2^T \tilde{R}_i(t)] \Sigma_2 \mathbb{E} [x_2 \tau(x_2)] = o(n^{-2} H_{\Sigma_1}(t_{11}) \text{tr}(\Sigma_2^2))$。

  其中，$\tilde{R}_i(t) = \psi_{\tau, i}(\alpha_{\tau} + n^{-1} t_0, \beta_{\tau} + n^{-1} t_1) - \psi_{\tau, i}(\alpha_{\tau}, \beta_{\tau})$，$H_{\Sigma_1} (t_{11}) = t_0^2 + t_{11}^T \Sigma_1 t_{11}$，$\Sigma_1$ 是 $x_{1i}$ 的协方差矩阵。

---

**定理 2.** 如果条件 (C.1) 和 (C.4)-(C.6) 成立，并且 $(\hat{\alpha}_{\tau} - \alpha_{\tau})^2 + (\hat{\beta}_{\tau, 1} - \bar{\beta}_{\tau, 1})^T \Sigma_1 (\hat{\beta}_{\tau, 1} - \bar{\beta}_{\tau, 1}) = O_p(n^{-1})$，则有：

 (a) 在原假设$H_0$ 下，

$$
\frac{n T_{1n,p}}{\tau(1 - \tau) \sqrt{2 \text{tr}(\Sigma_2^2)}} \overset{L}{\to} N(0, 1);
$$

 (b) 在 $H_1$ 下，

$$
\frac{n(T_{1n,p} - \mu_{1n,p})}{\tau(1 - \tau) \sqrt{2 \text{tr}(\Sigma_2^2)}} \overset{L}{\to} N(0, 1),
$$

其中
$$
\mu_{1n,p} = \frac{n-1}{n} \lVert \mathbb{E} x_{2i} \tau(x_{2i}) \rVert^2 - \frac{1}{n} \left( \bar{\tau} \mathbb{E} x_{2i}^T x_{2i} \tau(x_{2i}) - \bar{\tau}^2 \text{tr}(\Sigma_2) \right).
$$

---

### 3.1 椭圆分布族下的情形

- 在本节中，将独立成分模型 (ICM) (C.4) 替换为椭圆分布族。这个分布族包括许多常见的多变量分布，如多变量正态分布、多变量 t 分布、多变量 logistic 分布以及一些其他重尾的多变量分布。椭圆分布和 ICM 的唯一交集是多变量正态分布，因此将条件 C.4 调整为

  **(C.4')** 假设向量 $\mathbf{x}$ 可以表示为 $\mathbf{x} = \mu + \Gamma R \mathbf{u}$，其中 $\Gamma$ 是一个 $p \times p$ 矩阵，$\mathbf{u}$ 是一个在 $\mathbb{R}^p$ 的单位球面上均匀分布的随机向量，$R$ 是一个与 $\mathbf{u}$ 独立的非负随机变量。此外，假设 $\mathbb{E} R^2 = p$ 且 $\text{Var}(R^2) = O(p)$

- 通过将定理 1 和定理 2 中的条件 (C.4) 替换为椭圆分布条件 (C.4')，可以类似地得到以下定理：

  **定理 3** 假设条件 (C.1)、(C.4')、(C.5) 和 (C.6) 成立。

  (a) 如果 $\lvert \hat{\alpha}_{\tau} - \bar{\alpha}_{\tau} \rvert = O_p(n^{-1/2})$，则定理 1 中的结论仍然成立；

  (b) 如果 $(\hat{\alpha}_{\tau} - \bar{\alpha}_{\tau})^2 + (\hat{\beta}_{\tau,1} - \bar{\beta}_{\tau,1})^T \Sigma_1 (\hat{\beta}_{\tau,1} - \bar{\beta}_{\tau,1}) = O_p(n^{-1})$，则定理 2 中的结论仍然成立。

---

### 3.2 $\text{tr}(\Sigma^2)$的估计

为了推导检验统计量 $T_{n,p}$ 的极限分布，需要估计未知的扰动参数 $\text{tr}(\Sigma^2)$ 在渐近方差中的值。本文提供了文献中两个常用的估计量。
- 第一个估计量是由 Chen 和 Qin (2010) 提出的，定义为

  $$
  \widehat{\text{tr}(\Sigma^2)} = \frac{1}{n(n-1)} \text{tr} \left( \sum_{j \neq k} (x_j - \bar{x}_{(j,k)}) x_j^T (x_k - \bar{x}_{(j,k)}) x_k^T \right)$$
  $$= a_2 \sum_{i \neq j} (x_i^T x_j)^2 - 2a_3 \sum_{i \neq j \neq k} x_j^T x_k + a_4 \sum_{i \neq j \neq k \neq l} x_j^T x_k x_j^T x_k, \tag{11}
  $$

  其中 $\bar{x}_{(j,k)}$ 表示在排除 $x_j$ 和 $x_k$ 后的样本均值，且 $a_k = \frac{1}{n(n-1) \cdots (n-k+1)}$。

---

- 根据 Zheng 等 (2020) 的定理 2.2，在忽略高阶项的情况下，另一个估计量为

  $$
  \widehat{\text{tr}(\Sigma^2)} = \left( 1 + \frac{1}{n-1} \right)^{-1} \left( \text{tr}(S_n^2) - \frac{1}{n-1} (\text{tr}(S_n))^2 \right), \tag{12}
  $$

  其中 $S_n$ 是协变量 $x$ 的样本协方差矩阵。由于此估计量通过样本与维度比值 $p/(n-1)$ 校准，因此当 $p$ 接近于 $n$ 且 $n$ 足够大时，推荐使用这个估计量。
- 在数值研究中，为了便于与现有方法进行比较，在与现有方法比较时使用估计量 (11)。

  最后，如果

  $$
  n T_{n,p} \geq \tau(1-\tau) \sqrt{2 \text{tr}(\widehat{\Sigma^2})} z_\alpha,
  $$
  则在置信水平 $\alpha$ 下拒绝 $H_0$，其中 $z_\alpha$ 是标准正态分布的上 $\alpha$ 分位点。

---

## 4. 数值模拟

在数值模拟中，研究了所提出检验方法在有限样本下的表现。首先生成自线性模型的 $n$ 个随机样本：

$$
y_i = \alpha + x_i^T \beta + \varepsilon_i, \quad i = 1, \dots, n, \tag{13}
$$

其中 $x_i$ 是 $p$ 维向量，随机误差 $\varepsilon_i$ 服从连续分布 $F$ 且满足 $F(0) = P(\varepsilon_i \leq 0) = \tau$。给定分位数回归损失函数 $\rho_\tau (u) = u(\tau - I\{u \leq 0\})$

$$
H_0 : \beta_{\tau} = 0 \quad \text{v.s.} \quad H_1 : \beta_{\tau} = \beta_1 \neq 0. \tag{14}
$$

在零假设下，截距 $\alpha_\tau$ 由观测值 $y_i$ 的样本 $\tau$ 分位数估计得到，其中 $i = 1, \dots, n$，$\hat{\alpha}_\tau = \hat{Q}_y(\tau)$。本文研究了不同场景下所提出检验方法的经验显著性水平和功效。为了展示稳健性，本文考察了一些重尾误差分布下的检验表现。对于每种设置进行了 1000 次蒙特卡洛模拟。蒙特卡洛模拟误差率为 $1.96 \sqrt{0.05 \times 0.95 / 1000} \approx 0.0135$，置信水平为 0.05。

---

### 4.1 模型设置

- **(1) 设计矩阵**
生成设计矩阵 $\mathbf{X} = (\mathbf{x}_1, \ldots, \mathbf{x}_n)^T$考虑常见的两种协方差结构。一种是高维回归中常用的结构，另一种则采用自 Zhong 和 Chen (2011) 的带状结构。
  **(i) 自回归结构**：假设样本 $\mathbf{x}_i$ 从均值为零、协方差矩阵为 $\Sigma = \{ \sigma_{ij} = \rho^{|i-j|}, \, i, j = 1, \dots, p \}$ 的 $p$ 维正态分布中提取，其中 $\rho$ 控制相关性强度。
  **(ii) 带状结构**：假设
  $$
  x_{ij} = \mu_j + \sum_{t=1}^{T} \rho_t z_{i,j+t-1}, \quad j = 1, \dots, p, \, i = 1,  \dots, n, \quad T < p.\tag{15}
  $$
  矩阵 $\mathbf{x}$ 的协方差矩阵为 $(\Sigma)_{kl} = \sigma_{kl} = \sum_{i=1}^T \rho_i \rho_{|k-l|} I\{|k-l| < T\}$，$k, l = 1, \dots, p$。$\Sigma$ 具有带状结构，且 $T$ 控制带宽（参考 Zhong 和 Chen, 2011）。在本研究中，生成 $\mathbf{z}_i$ 服从 $N(0, I_{p+T-1})$。$\mu_j, j = 1, \dots, p$ 独立于 $u$，并从均匀分布 $Unif[2,3]$ 中提取。$\rho_t = t / (T+1)$，$t = 1, \dots, T$。

- **(2) 误差分布**
考虑常见的几种分布，包括正态分布、Laplace 分布（双指数分布）、逻辑斯蒂分布、Cauchy ($t_1$) 分布、Student's $t_q, q = 2$ 分布以及混合正态分布。

---

- **(3) 参数 $\beta$**
假设非零系数为 $S = \{1, 2, \dots, s\}, s < p$，每个系数的大小为 $\beta_j = \|\beta_S\| / \sqrt{s}$。取 $s = 2, 10$ 作为点稀疏模式，$s = p/2$ 作为比例稀疏模式。具体而言，系数的设置如下：
 (i) 点稀疏 ($s = 2$)：$\beta_1 = \beta_2 = \|\beta\| / \sqrt{2}$，$\beta_3 = \cdots = \beta_p = 0$；
 (ii) 比例稀疏：$\beta_1 = \cdots = \|\beta\| / \sqrt{p/2}$，$\beta_{p/2+1} = \cdots = \beta_p = 0$。

  对于部分分位数回归检验，考虑一个稀疏线性模型：

  $$
  y_i = \alpha_{\tau} + x_{1i}^T \beta_{\tau, 1} + x_{2i}^T \beta_{\tau, 2} + \varepsilon, \tag{16}
  $$

  其中 $\beta_{\tau, 2}$ 在零假设下为零。通过改变稀疏模式和 $\ell_2$-范数来研究部分分位数回归检验的功效。
  对于 $\beta_{\tau, 1}$，本文考虑两种不同的情况：
  **(i) 非稀疏**：$\beta_{\tau, 1j}, j = 1, \dots, p_1$ 从 $Unif(0, 1)$ 中抽取，且 $\|\beta_{\tau, 1}\|_0 = p_1$；
  **(ii) 点稀疏**：$\beta_{\tau, 1} = (3, 1.5, 0, 0, 2)^T$，即 $\|\beta_{\tau, 1}\|_0 = 3$。

---

### 4.2 数值模拟结果

本文考虑了高维设置 $(n, p) = (40, 310), (60, 400), (80, 550)$，它们都满足指数关系 $p = \exp(n^{0.4}) + 230$。$\tau = 0.25, 0.5, 0.75$，本文仅展示 $\tau = 0.5$ 的结果，因为在不同分位数下，测试结果大致相同。

本文考虑了四种常见误差分布：
(a) 正态分布，$N(0, 3)$；
(b) Laplace 分布，密度函数为 $f(x) = \exp(-|x| / 2)$；
(c) Logistic 分布，密度函数为 $f(x) = \exp(x)(1 + \exp(x))^{-2}$；
(d) Cauchy 分布，密度函数为 $f(x) = \pi^{-1}(1 + x^2)^{-1}$。

---

![bg fit left](./images/Rplot_kmeans_result.png)
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;测试全模型显著性的结果在 Tables 1-3 中展示。Tables 1-3 显示了不同稀疏模式和不同分布的结果，基于 1000 次重复实验。提出的检验方法很好地控制了 I 类错误率。为了实现相似的功效，点稀疏模式的 $\|\beta\|$ 是比例稀疏模式的两倍。对于每个备择假设，在样本量足够大的情况下，即使维度很高，$\operatorname{tr}(\Sigma^2)$ 的估计也表现良好。

---
![bg fit left vertical](./images/table3.jpg)
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Table 3 补充了 Tables 1 和 2 的结果，其中较大的 $\rho$ ($\rho = 0.6$) 可以使统计功效迅速增加。

---
![bg fit left vertical](./images/table4.jpg)
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;接下来，本文研究了带状设计矩阵下提出的检验方法的性能，结果展示在 Table 4 中。带宽设置为 $T = 5$，$\beta$ 具有 2 或 10 个非零分量。本文仍然旨在测试全模型的显著性。由于带状设计矩阵满足定理的条件，测试结果显示出良好的经验显著性水平和功效，并且对 $\operatorname{tr}(\Sigma^2)$ 的估计也表现得非常好。

---
![bg fit left vertical](./images/table5.jpg)
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;本文研究了所提方法在重尾分布（包括正态分布、Laplace 分布、Student's $t_2$ 和 $t_3$ 分布）下的稳健性。将提出的检验方法与三种现有的高维检验方法进行了比较：Zhong 和 Chen (2011)、Cui 等 (2018) 以及 Zhang 等 (2018)。比较方法的结果分别标记为 ZC、CGZ 和 ZYS，展示在 Table 5 中。在重尾分布 $t_2$ 和 $t_3$ 下，其表现优于 ZC 和 CGZ 方法，而在正态和 Laplace 分布下则表现类似。ZYS 方法在样本量较小时对大多数全局检验表现良好，这可能是由于其对 Bootstrap 的依赖。然而，在几乎所有情境下，本文提出的方法的功效高于 ZYS。

---
![bg fit left vertical](./images/table6.jpg)
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;本文考察了所提出的部分分位数回归检验在有限样本下的性能。带宽设置为 $T = 5, 10, 15$。设置 $\|\beta_1\|_0 = 5, 10, 15$，其中 $\|\beta_1\|_0$ 表示向量中非零分量的数量。选择 $\|\beta_2\|$ 为 0.5, 1, 2，$\|\beta_2\|$ 取值为 10, 20, 50, 100 的备择假设。分位数 $\tau = 0.25, 0.5, 0.75$。由于首先需要估计系数参数 $\beta_{\tau,1}$，本文将样本量增加至 80, 120, 160。所有结果基于 1000 次重复实验，结果展示在 Tables 6-8 中。结果显示，部分模型显著性检验能够有效控制 I 类错误率，无论分布是否为重尾，以及分位数如何选择。此外，随着 $\|\beta\|$ 增加，统计功效迅速增加。

---
![bg fit left vertical](./images/table12.jpg)


- 随后将所提出的方法与基于最大范数和自举技术的检验方法进行了对比 (Tang et al., 2020)。这种方法的优势在于它在高维设定下易于捕获稀疏的强信号。由于应用了自举方法，它在小样本情况下也有一定优势，但计算成本更高。

- 本文方法被标记为“QR”，对比的方法被标记为“MAX”。结果再次显示，本文的检验方法在不同的情形下仍能控制 I 类错误率，并具有较高的功效。这与理论分析一致。当样本量较小时，“MAX” 方法在信号强度较高（$s_2$ 较小）以及误差服从高斯分布时更具优势。但对于 $t_3$ 分布，“MAX” 方法在某些情况下未能控制 I 类错误率。相比之下，本文的方法更加稳定有效，且计算速度更快。
---

- 根据 Monte Carlo 模拟的结果，本文提出的高维分位数回归检验在不同情形下具有良好的经验显著性水平和功效，对重尾分布也表现出稳健性。

---

## 5.实证研究
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;本文通过对一个金融数据集的实证分析来展示所提出的检验方法。数据来源于中国股票市场中 CSI 300 指数成分股从 2006 年 9 月 1 日到 2009 年 12 月 25 日的每周收益，这段时间涵盖了一次次贷危机。由于样本稳定性和动态跟踪的原则，CSI 300 指数每六个月会调整其成分股。经过数据清洗后，使用的数据集包含 97 只股票的 132 个每周收益。研究 CSI 300 指数上市开放式基金（LOF，代码：SZ.160706）与相应成分股的收益关系具有重要意义。

---
![bg fit left vertical](./images/fig1.jpg)

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;本文讨论了 LOF 收益与成分股收益的初步分析。LOF 是一个由投资者资金汇集而成的多样化投资组合，可以发行无限数量的份额。基金发起人直接向投资者出售并赎回份额。与另一种流行的开放式交易型基金（ETF）不同，LOF 可以是被动的指数基金，也可以是主动配置基金，只涉及现金和基金份额的交易。图 1 显示了标准化后的每周 CSI 300 指数及其相应的 LOF 价格。虽然 LOF 能跟踪 CSI 300 指数，但受到其他市场因素的影响，使两者存在差异。

---
![bg fit left vertical](./images/fig2.jpg)

- 图 2 展示了 CSI 300 LOF 的收益序列及其 Hill 估计。可以注意到，收益序列的左右尾部分布不同，并且都是重尾分布（当样本量增大，Hill 估计值小于 2，用图 2 中的实线表示）。


- 鉴于这些特性，在不同的 $\tau \in (0, 1)$ 下考虑分位数回归：
  $$
  y_i = \alpha_\tau + \mathbf{x}_i^\top \boldsymbol{\beta_\tau} + \varepsilon_i, \tag{17}
  $$
  其中，$y_i$ 是 CSI 300 LOF 的第 $i$ 周收益，$\mathbf{x}_i \in \mathbb{R}^{97}$ 是 97 只成分股的第 $i$ 周收益。

---
![bg fit left vertical](./images/table9.jpg)

- 首先，检验完整模型在 $\tau = 0.1, 0.25, 0.5, 0.75, 0.9$ 下的显著性。结果如表 9 所示。在不同分位数下的检验均拒绝了原假设，表明成分股收益与 LOF 收益之间存在较强的相关性。即使在较少的观测和更多的股票情况下，仍然观察到类似的相关性。

---

- 接下来，本文拟合了三个经典模型，以去除这些收益中一些市场共同因子的影响。用于构建这些模型的数据来自一个商业数据库。设向量 $\mathbf{r}_i \in \mathbb{R}^{98}$ 表示 LOF 收益和第 $i$ 周的成分股收益。令 $\mathbf{z}_1$ 表示市值加权股票市场与现金之间的收益差（即市场因子），$\mathbf{z}_2$ 表示小公司减去大公司收益的差异（即规模效应），$\mathbf{z}_3$ 表示便宜公司减去昂贵公司收益的差异（即价值效应），$\mathbf{z}_4$ 表示高表现公司减去低表现公司收益的差异（即动量效应）。

- 考虑资本资产定价模型（CAPM，Markowitz, 1952; Sharpe, 1964; Lintner, 1965）：

$$
\mathbf{r}_i = \mathbf{b}_{10} + \mathbf{b}_{11}\mathbf{z}_{1i} + \mathbf{e}_{1i};
\tag{18}
$$

- Fama 和 French 的三因子模型（TFM，Fama and French, 1993, 1996）：
  $$
  \mathbf{r}_i = \mathbf{b}_{20} + \mathbf{b}_{21}\tilde{\mathbf{z}}_i + \mathbf{e}_{2i},\tag{19}
  $$
  其中 $\tilde{\mathbf{z}}_i = (\mathbf{z}_{1i}, \mathbf{z}_{2i}, \mathbf{z}_{3i})^T$；

- Carhart 的四因子模型（CFM，Carhart, 1997）：

  $$
  \mathbf{r}_i = \mathbf{b}_{30} + \mathbf{b}_{31}\bar{\mathbf{z}}_i + \mathbf{e}_{3i},\tag{20}
  $$

  其中 $\bar{\mathbf{z}}_i = (\mathbf{z}_{1i}, \mathbf{z}_{2i}, \mathbf{z}_{3i}, \mathbf{z}_{4i})^T$。

---
![bg fit left vertical](./images/table10.jpg)

&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;本文感兴趣的是在去除市场共同因子后，LOF 收益和成分股收益之间是否仍然存在相关性。为此，本文将这个问题表述为基于模型 (18) 至 (20) 残差来检验模型 (17) 的显著性，再次对不同分位数应用所提出的检验方法。结果列在表 10 中。所有检验都接受了原假设，这表明所选模型的残差与非显著变量集之间没有显著相关性，因此 LOF 收益和成分股收益之间的所有相关性都来自市场共同因子。实证分析表明，这三个经典定价模型在一定程度上可以从更广泛的角度解释中国股票收益的行为。

---

## 6.结论
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;本文研究了高维分位数回归中的显著性检验问题。受高维双样本均值检验的启发，提出了一种基于分位数评分函数的新检验方法。该方法被视为经典秩评分检验在高维环境下的改进与拓展。本文将检验扩展用于评估部分线性模型的显著性，尤其是在有若干重要变量时。此外，本文推导了检验统计量在原假设和备择假设下的渐近分布。当设计矩阵服从椭圆分布时，这些渐近分布为理论提供了有价值的补充。模拟结果显示，极限原假设分布能够很好地逼近实际分布，相应的渐近临界值有效地保持了 I 型错误率。此外，本文的方法对局部备择假设非常敏感。与一些现有方法相比，本文的检验在处理非高斯重尾数据时表现出了更强的统计功效。实证分析进一步证明，分位数回归在计量经济学和金融计量经济学中具有重要应用。通过检验多因子定价模型在中国股票市场中不同分位数下的显著性，本文验证了经典有效市场假设的有效性。




