---
marp: true
# theme: suibeppt
math: mathjax
style: |
  .authors-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin: 20px 0;
    text-align: center;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
  }
  .author {
    padding: 10px;
    border-radius: 8px;
    /* background-color: rgba(240, 240, 240, 0.3); */
    /* border: 1px solid rgba(200, 200, 200, 0.5); */
  }
  .author-name {
    font-weight: bold;
    margin-bottom: 6px;
    font-size: 1.0em;
    color: #333;
  }
  .author-info {
    font-size: 0.85em;
    line-height: 1.2;
    color: #555;
  }
  /* 强制网格布局 */
  .authors-grid > .author:nth-child(1) { grid-column: 1; grid-row: 1; }
  .authors-grid > .author:nth-child(2) { grid-column: 2; grid-row: 1; }
  .authors-grid > .author:nth-child(3) { grid-column: 3; grid-row: 1; }
  .authors-grid > .author:nth-child(4) { grid-column: 1; grid-row: 2; }
  .authors-grid > .author:nth-child(5) { grid-column: 2; grid-row: 2; }
  .authors-grid > .author:nth-child(6) { grid-column: 3; grid-row: 2; }
  /* 最后一行居中显示 */
  .authors-grid > .author:nth-child(7) { grid-column: 1; grid-row: 3; grid-column-start: 1; grid-column-end: 2; }
  .authors-grid > .author:nth-child(8) { grid-column: 3; grid-row: 3; grid-column-start: 3; grid-column-end: 4; }
  /* 响应式设计：在较小屏幕上改为2列 */
  @media (max-width: 800px) {
    .authors-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
    }
    .authors-grid > .author:nth-child(1) { grid-column: 1; grid-row: 1; }
    .authors-grid > .author:nth-child(2) { grid-column: 2; grid-row: 1; }
    .authors-grid > .author:nth-child(3) { grid-column: 1; grid-row: 2; }
    .authors-grid > .author:nth-child(4) { grid-column: 2; grid-row: 2; }
    .authors-grid > .author:nth-child(5) { grid-column: 1; grid-row: 3; }
    .authors-grid > .author:nth-child(6) { grid-column: 2; grid-row: 3; }
    .authors-grid > .author:nth-child(7) { grid-column: 1; grid-row: 4; }
    .authors-grid > .author:nth-child(8) { grid-column: 2; grid-row: 4; }
  }
  /* 在很小的屏幕上改为1列 */
  @media (max-width: 500px) {
    .authors-grid {
      grid-template-columns: 1fr;
    }
    .authors-grid > .author { grid-column: 1 !important; }
  }

  /* 强制所有页面内容顶部对齐 */
  section {
    justify-content: flex-start !important;
    align-items: flex-start !important;
    display: flex !important;
    flex-direction: column !important;
    padding-top: 30px !important;
  }

  /* 首页标题居中 */
  section:first-of-type h1 {
    text-align: center !important;
    width: 100% !important;
    margin-bottom: 15px !important;
  }

  /* 确保段落从顶部开始 */
  section > * {
    margin-top: 0 !important;
  }

  /* 为内容少的页面添加最小高度 */
  section p {
    min-height: auto;
  }

  /* 只针对块级数学公式($$...$$)居中显示，不影响行内公式($...$) */

  /* KaTeX 块级公式居中 */
  .katex-display {
    text-align: center !important;
    margin: 1.5em auto !important;
    display: block !important;
  }

  /* MathJax 块级公式居中 */
  .MathJax_Display {
    text-align: center !important;
    margin: 1.5em auto !important;
    display: block !important;
  }

  /* 现代 MathJax 块级公式居中 */
  mjx-container[display="true"] {
    text-align: center !important;
    margin: 1.5em auto !important;
    display: block !important;
  }

  /* 通用块级数学公式类 */
  .math-display,
  .math.display {
    text-align: center !important;
    margin: 1.5em auto !important;
    display: block !important;
  }

  /* 确保在section中的块级公式居中 */
  section .katex-display,
  section .MathJax_Display,
  section mjx-container[display="true"] {
    text-align: center !important;
  }

  /* 不影响行内公式 */
  .katex:not(.katex-display),
  .MathJax:not(.MathJax_Display),
  mjx-container:not([display="true"]) {
    display: inline !important;
    text-align: inherit !important;
  }

  /* 参考suibeppt.css的数学公式居中方法 */
  .MathJax,
  .MathJax_Display,
  mjx-container[display="true"],
  mjx-container[jax="CHTML"][display="true"],
  .mjx-chtml[display="true"] {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    display: block !important;
    width: 100% !important;
    justify-self: center !important;
    align-self: center !important;
  }

  /* 行内数学公式保持原样 */
  mjx-container[display="false"],
  mjx-container[jax="CHTML"][display="false"],
  .mjx-chtml[display="false"] {
    display: inline !important;
    text-align: inherit !important;
  }

  /* 确保包含$$公式的段落居中 */
  p:has(mjx-container[display="true"]),
  div:has(mjx-container[display="true"]),
  section p:has(mjx-container[display="true"]),
  section div:has(mjx-container[display="true"]) {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    width: 100% !important;
  }

---

# 用于推荐的大语言模型表征学习

# Representation Learning with Large Language Models for Recommendation 


<div class="authors-grid">
<div class="author">
<div class="author-name">任旭斌</div>
<div class="author-info">
香港大学<br>
<EMAIL>
</div>
</div>

<div class="author">
<div class="author-name">魏巍</div>
<div class="author-info">
香港大学<br>
<EMAIL>
</div>
</div>

<div class="author">
<div class="author-name">夏良浩</div>
<div class="author-info">
香港大学<br>
<EMAIL>
</div>
</div>

<div class="author">
<div class="author-name">苏立新</div>
<div class="author-info">
百度公司<br>
<EMAIL>
</div>
</div>

<div class="author">
<div class="author-name">程苏琪</div>
<div class="author-info">
百度公司<br>
<EMAIL>
</div>
</div>

<div class="author">
<div class="author-name">王俊峰</div>
<div class="author-info">
百度公司<br>
<EMAIL>
</div>
</div>

<div class="author">
<div class="author-name">尹大为</div>
<div class="author-info">
百度公司<br>
<EMAIL>
</div>
</div>

<div class="author">
<div class="author-name">黄超*</div>
<div class="author-info">
香港大学<br>
<EMAIL>
</div>
</div>
</div>

---


## ABSTRACT

 在深度学习和图神经网络的影响下,推荐系统取得了显著进展,尤其在捕捉复杂的用户-物品关系方面。然而,这些基于图的推荐器严重依赖基于ID的数据,可能会忽略与用户和物品相关的有价值文本信息,导致学习到的表示信息不足。此外,隐式反馈数据的使用会引入潜在的噪声和偏差,给用户偏好学习的有效性带来挑战。虽然将大语言模型 (LLM)集成到传统基于ID的推荐器中已受到关注,但要在实际推荐系统中有效实施,还需要解决可扩展性问题、仅依赖文本的局限性以及提示输入约束等挑战。为应对这些挑战,我们提出了一个与模型无关的框架RLMRec,旨在通过大语言模型赋能的表示学习来增强现有的推荐器。它提出了一种将表示学习与大语言模型相结合的推荐范式,以捕捉用户行为和偏好的复杂语义方面。RLMRec纳入辅助文本信号,利用大语言模型进行用户/物品画像,并通过跨视图对齐将大语言模型的语义空间与协作关系信号对齐。这项工作还通过互信息最大化证明了纳入文本信号的理论基础,从而提高了表示的质量。我们将RLMRec与最先进的推荐模型集成进行评估,同时分析其效率和对噪声数据的鲁棒性。实现代码可在https://github.com/HKUDS/RLMRec 获取。

---



## 1 引言

 推荐系统已发展为基于用户交互提供个性化项目推荐, 深度学习和图神经网络发挥了重要作用[4,39]。   像NGCF [35]和LightGCN [11]这样的基于图的推荐器在捕捉复杂的用户-项目关系方面展现出了令人印象深刻的能力, 使其成为了最先进的方法。 

 然而,需要注意的是,近期基于图的推荐器在学习过程中严重依赖ID对应信息。   该领域的训练数据仅包含映射后的用户/物品索引,它们之间的交互通过交互矩阵以二进制值表示(有交互为1,无交互为0)。   虽然这种数据安排已证明有效,但一个局限性在于它主要依赖基于ID的信息,可能会忽略其他有价值的数据,如与用户和物品相关的丰富文本信息。   缺少这些额外信息可能会导致学习到的表示信息不足。   此外,值得注意的是,这些基于图的推荐器中的大部分数据由隐式反馈 [27,33]组成,这可能会引入来自假阴性或偏差的噪声(例如,误点击[34]或流行度偏差[5])。   因此,这些基于图神经网络 (GNN)的模型所学习到的表示严重依赖于数据的固有质量。   对数据质量的高度依赖带来了潜在挑战,因为这可能导致产生有害的表示,从而阻碍推荐系统的有效性,尤其是当数据包含噪声时。 

---

 近年来,为了改进传统的基于身份的方法,人们进行了多项利用不同数据模式的尝试。   特别值得关注的是,像GPT-4 [24] 和LLaMA [32]这样的大语言模型(LLM)的出现,它们在神经语言理解任务中展现出了令人瞩目的能力。   这一发展引发了研究人员的极大兴趣,他们正积极探索在处理文本内容方面表现出色的大语言模型如何能将推荐系统的能力拓展到原始数据之外 [7,18,21]。   该领域当前研究的一个主要焦点是通过提示设计使推荐方法与语言模型的特性相匹配。   像 InstructRec [47]这样的方法将推荐任务构建成指令- 问答格式,使大语言模型能够同时实现推荐目标并回答精心设计的问题1,9 。   然而,这些方法在效率和精度方面仍落后于现有的推荐系统。   这可归因于这种方法存在的固有缺陷,主要包括以下几个关键方面: 

 i) 实际推荐系统中的可扩展性问题。在个性化用户行为建模中使用大语言模型(LLM)需要大量的计算资源。   随着用户行为数据规模的增长,计算需求和相关推理时间成本也会增加。   例如,在TALLRec [1]中,推荐是以指令问答的形式生成的,LLaMA2-13B为单个用户提供推荐的响应时间约为3.6秒,输入大小约为800个标记(相当于约5个用户)。   然而,当试图为拥有大量用户基础和广泛商品目录的实际推荐系统扩展该方法时,这会带来重大挑战。 

---

 ii) 仅依赖文本带来的局限性。由于幻觉问题[20],大语言模型可能会生成包含对不存在商品的推荐的文本答案。   这给确保生成推荐的准确性和可靠性带来了挑战。   此外,受最大标记数限制(例如,LLaMA为2048个标记)的提示输入容量有限,阻碍了对具有全局用户依赖关系的全面协作信号进行有效建模。 

 为验证上述局限性,我们评估了在亚马逊数据集上直接使用大语言模型(LLMs)来增强重排序任务[12,31]以进行推荐的有效性。   具体而言,我们使用LightGCN [11]作为骨干模型,该模型为每个用户检索出一个包含50个候选物品的排名列表。   为进一步优化推荐结果,我们将每个物品的文本信息与我们自定义的提示(详情请参考附录A.3)相结合。   然后,这些提示由ChatGPT (即gpt-3.5-turbo)进行处理。   此任务的目标是为每个用户对物品列表进行重排序,并找出前10和前20个最相关的物品。 

---


---

 从图1的结果可以明显看出,由ChatGPT优化后的推荐效果不如LightGCN提供的原始结果。   这表明在推荐的重排序过程中盲目使用大语言模型 (LLMs)存在局限性。   这些局限性可归因于三个因素:其一,大语言模型存在幻觉问题,会推荐不在候选集中的项目;其二,由于令牌限制,缺乏全面的基于全局文本的协作关系输入;其三,值得注意的是,使用大语言模型进行重排序过程需要数小时才能完成,这在现实世界的推荐场景中处理大规模数据时构成了挑战。   由于篇幅限制,我们在附录中提供了详细分析和示例,以展示幻觉现象。 

---

 **贡献。** 鉴于上述局限性,我们旨在利用大语言模型(LLMs)的能力无缝增强现有的推荐系统。   为实现这一目标,我们提出了一个与模型无关的框架,称为RLMRec(基于大语言模型进行表征学习的推荐框架)。   RLMRec的核心思想是利用表征学习作为基于ID的推荐器和大语言模型之间的桥梁。   我们的新推荐范式旨在保留现有推荐器的准确性和效率,同时利用大语言模型强大的文本理解能力来理解用户行为和偏好的复杂语义方面。   首先,我们通过对纳入辅助文本信号进行表征学习的益处进行建模,奠定了理论基础。   这包括将文本信号转化为有意义的表征,并为在通用推荐模型中最大化互信息建立理论基础。   此外,我们开发了一种由大语言模型驱动的用户/物品画像范式,通过纳入来自大语言模型全局知识空间的全面语义理解来增强表征表达能力。 

---

 此外,我们提议通过跨视图对齐框架来对齐大语言模型 (LLMs)的语义空间和协作关系信号的表示空间。   这种对齐是通过跨视图互信息最大化方案实现的,该方案使我们能够找到一个共同的语义子空间,在这个子空间中,文本嵌入和协作关系嵌入分别从对比建模和生成建模的角度实现了良好的对齐。   简而言之,我们的主要贡献总结如下: 

*  本工作旨在通过利用大语言模型(LLMs)并将其语义空间与协作关系建模对齐以实现更好的表示学习,探索提升现有推荐系统推荐性能的潜力。 
*  我们提出了一个与模型无关的表示学习框架,称为 RLMRec,该框架以我们的理论研究结果为指导。   此框架利用对比或生成建模技术来提升所学表示的质量。 
*  我们建立了一个理论基础,以证明纳入文本信号在提升表示学习方面的有效性。   通过利用互信息最大化,我们展示了文本信号如何提高表示质量。 
*  我们将RLMRec与各种先进的推荐模型集成,并验证了我们方法的有效性。   此外,我们分析了该框架对噪声和不完整数据的鲁棒性,展示了其应对现实世界挑战的能力。 

---

## 2 相关工作

 **图神经网络增强的协同过滤。** 协同过滤(CF)作为推荐系统中的一项基础技术,已得到广泛研究[16,30]。   一个新兴的研究方向是利用历史用户-物品交互数据构建二分图,并采用图神经网络(GNN)来捕捉高阶协同关系。   诸如NGCF [35]、GCCF [6]、LightGCN [11]等基于图的方法已展现出了最先进的性能,提高了推荐效果。   然而,隐式反馈数据中的稀疏性和噪声给基于图的方法带来了挑战。   为解决这一问题,研究人员探索使用自监督学习(SSL)技术作为辅助学习目标,以增强推荐的鲁棒性[43,45]。   在各种SSL技术中,对比学习已成为协同过滤模型中的一种突出解决方案。   像SGL [37]、SimGCL [44]、NCL [19]、LightGCL [3]等方法利用对比数据增强来提高推荐性能。   在这项工作中,我们更进一步,将大语言模型(LLM)与现有的CF模型相结合,以有效整合LLM的知识和推理能力与协同关系学习,从而提升推荐性能。 

---

 **用于推荐的大语言模型。** 利用大语言模型构建推荐系统已引发关注 [ $[7,18,21,38]$。   多项研究通过设计使大语言模型与推荐任务相匹配的提示,将其用作推理模型。   例如, P5 [9] 使用物品索引将用户交互数据转换为文本提示,然后用于语言模型训练。   Chat - REC [8] 通过将用户画像和交互转换为提示,供大语言模型生成推荐,从而构建了一个对话式推荐器。   InstructRec [47] 和TALLRec [1] 采用指令设计来定义推荐任务,并微调大语言模型以使其与这些指令相匹配,从而生成推荐。   然而,直接使用大语言模型进行推荐任务面临着计算成本高和推理时间长等挑战。   为解决这一问题,我们的方法采用互信息最大化,使大语言模型的知识与协同关系建模相匹配,从而实现可扩展且有效的推荐。 

---

## 3 方法

### 3.1 RLMRec的理论基础

 **协同过滤。** 在我们的推荐场景中,我们有一组用户 $\mathcal{U}=u_{1},...,u_{I}$ 和一组物品 $\mathcal{V}=v_{1},...,v_{J}$ 观察到的用户- 物品交互由X表示。   在基于学习的推荐系统中,每个用户和物品都被赋予初始嵌入 $x_{u}$ 和 $x_{v}$。   目标是通过一个推荐模型(即 $e_{u},e_{v}=\mathcal{R}(x_{u},x_{v})$)学习用户和物品的表示,该模型能使以下后验分布最大化: 

$$p(e|X)\propto p(X|e)p(e)$$

 在实际推荐场景中,观察到的用户- 物品交互X通常包含噪声,包括误报(例如,误点击或受流行度偏差影响的交互)和漏报(例如,用户未与未见过但感兴趣的物品进行交互)。   因此,学习到的表示e也会受到这种噪声的影响,从而对推荐准确性产生负面影响。   在这项工作中,我们引入了一种内在的隐藏先验信念z,这对推荐有益。   这种先验信念有助于识别中的真正正样本。   因此,表示的生成涉及有利的先验信念z与学习过程中不可避免的噪声的结合。 

---



 ---

 **文本增强的用户偏好学习。** 为减轻无关信号对表征的影响,有必要纳入辅助信息线索。   一种方法是引入文本信息,例如用户和物品的简介,这些信息为用户偏好学习提供了见解。   可以使用语言模型对这些简介进行编码,以生成能够有效捕捉用户偏好语义方面的表征 $s\in\mathbb{R}^{d_{s}}$。   重要的是,s和e都捕捉到了与用户- 物品交互相关方面的共享信息。   这种共享信息至关重要,因为它表明纳入了对推荐有益的方面,与先验信念 $z-$ 借助协作侧表示和文本侧表示s,二者均包含从z生成 的对推荐有益的信息,我们的目标是通过最大化条件概率来学习记为e*的e的最优值: 
$$e^{*}=arg~max_{e}\mathbb{E}_{p(e,s)}[p(z,sle)]. \quad (2)$$

 最大化条件概率背后的基本直觉是确保推荐模型的可学习表示包含从先验信念z生成的更纯粹的信息以及与语义表示s的共享信息。   通过这样做,所学习的表示对推荐的相关性和益处得以增强。 

 ---

 **定理1.** 在给定隐藏先验信念z的情况下最大化后验概率 $\mathbb{E}_{p(e,s)}[p(z,s|e)]$,等同于最大化CF端关系表示 e与大语言模型(LLM)端语义表示s之间的互信息 $I(e;s)$。 

 **证明。** 需要注意的是,由于用户和物品的特征是固定的,概率 $p(s)$ 在学习过程中保持不变。   因此,我们可以得出以下结论: 

 $$\mathbb{E}_{p(e,s)}[p(z,sle)]\propto\mathbb{B}_{p(e,s)}log[\int_{z}\frac{p(z,sle)}{p(s)}dz]. \quad (3)$$
 $$=\mathbb{B}_{p(e,s)}log[\frac{\int_{z}p(z,e|s)dz}{p(e)}]. \quad (4)$$
 $$=\mathbb{B}_{p(e,s)}log[\frac{p(e/s)}{p(e)}]=I(e,s). \quad (5)$$

 让我们将e和s视为数据样本,假设我们有e和s的对对应元素,分别构成集合 $E=\{e_{1},...,e_{i},...,e_{N}\}$ 和 $S=\{s_{1},...,s_{i},...,s_{N}\}$。   基于此,我们按如下方式优化互信息。 

---



---
 **定理2.** 通过引入密度比来保留互信息[23] $f(s,e)\propto p(s|e)/p(s)$, $I(e_{i};s_{i})$ 的最大化问题可重新表述为最大化以下下界: 
 $$\mathbb{E}log[\frac{f(s_{i},e_{i})}{\sum_{s_{j}\in S}f(s_{j},e_{i})}]. \quad (6)$$

 **证明.** 基于互信息的性质,我们有 $I(e_i, s_i) = I(s_i, e_i)$。考虑到这一点,我们进行如下推导:
 $$I(s_i, e_i) \geq I(s_i, e_i) - \log(N) = -\mathbb{E}\log[\frac{p(s_i)}{p(s_i|e_i)}N] \quad (7)$$
 $$\geq -\mathbb{E}\log[1 + \frac{p(s_i)}{p(s_i|e_i)}(N-1)] \quad (8)$$
 $$= -\mathbb{E}\log[1 + \frac{p(s_i)}{p(s_i|e_i)}(N-1)\mathbb{E}_{s_j \in S_{neg}}\frac{p(s_j|e_i)}{p(s_j)}] \quad (9)$$

---

 $$\approx -\mathbb{E}\log[1 + \frac{p(s_i)}{p(s_i|e_i)}\sum_{s_j \in S_{neg}}\frac{p(s_j|e_i)}{p(s_j)}] \quad (10)$$
 $$= \mathbb{E}\log[\frac{f(s_i, e_i)}{\sum_{s_j \in S}f(s_j, e_i)}]. \quad (11)$$


 在此, $S_{neg}$ 表示考虑第 个样本(即 $S_{neg}=S\backslash s_{i})$ 时的负样本。   到目前为止,我们从理论角度推导了如何通过引入外部知识来减轻表征中的噪声影响。   然而,这种方法也带来了两个挑战: ) 挑战1:如何获得能够捕捉用户和物品交互偏好的有效描述。   ii) 挑战2:如何有效地对密度比 $f(s,e)$ 进行建模,以最大化e和s之间的互信息。   在接下来的章节中,我们将讨论解决这两个挑战的潜在方案。 

---

### 3.2 用户/物品画像范式

 在我们之前的推导中,我们强调了为用户和物品获取文本描述 (即简档)的重要性。   这些简档在减轻推荐系统学习表示中的噪声影响方面起着至关重要的作用,并能让我们从语义上理解用户和物品的交互偏好。   理想情况下,用户和物品简档应具备以下特征: 

*  **用户简档:**应能有效概括用户倾向于喜欢的特定类型的物品,从而全面呈现他们的个性化品味和偏好。 
*  **物品简档:**应能清晰阐明该物品易于吸引的特定类型的用户,明确呈现该物品与这些用户的偏好和兴趣相契合的特征和品质。 

---

 在某些情况下,原始数据可能包含与用户和物品相关的文本属性。   例如,在Yelp数据集中,用户会对去过的商家进行评价,而商家具有位置和类别等属性。   然而,此类文本数据通常包含多余的噪声,导致常见的困境:
 i)属性缺失:某些物品或用户的部分属性可能缺失;
 ii) 文本数据含噪:文本本身可能被大量与用户偏好无关的噪声污染。   例如,在Steam数据集中,用户对游戏的评价可能包含大量特殊符号或无关信息。   这些挑战使得难以从文本中提取有用的用户和物品特征。   因此,现有模型通常将低噪声属性转换为独热编码,而未能有效利用文本数据中的语义信息。 

---

 幸运的是,大型语言模型(LLM)的最新进展释放了其卓越的文本处理能力,使其能够处理广泛的自然语言处理(NLP)任务,包括文本去噪和摘要生成。   这一关键进展为从数据集中固有的嘈杂文本特征生成用户和物品画像开辟了新的可能性。   我们利用大型语言模型的巨大潜力,提出了一种利用协作信息进行画像生成的范式。   考虑到与用户属性相比,数据集通常包含更多关于物品属性的文本描述,我们的方法采用了从物品到用户的视角,具体如下。 

---

#### 3.2.1 通过推理生成画像。

 近期研究表明,在大语言模型中融入过程推理可有效减少幻觉现象并提高生成输出的质量。   基于这些研究成果,我们精心设计了系统提示 $\mathcal{S}_{u/v}$,并将其作为提供给大语言模型的输入的一部分。   其目的是通过精确指定输入-输出内容和期望的输出格式,明确其为用户生成用户画像或为物品生成物品画像的功能。   重要的是,我们明确强调将推理过程作为生成输出的一个组成部分。   通过将此系统提示与用户/物品画像生成提示 $Q_{u}$ 和 $Q_{v}$ 相结合,我们可以利用大语言模型生成准确的画像。   具体过程如下: 
 $$\mathcal{P}_{u}=LLMs(S_{u},Q_{u}),$ $\mathcal{P}_{v}=LLMs(S_{v},Q_{v}). \quad (12)$$ 

---

#### 3.2.2 条目提示构建。

 我们将条目 $v\in\mathcal{V}$ 的文本信息分为四类:标题、原始描述 $\beta,$ 特定数据集属性 $\gamma=\gamma_{1},...,\gamma_{|\gamma|}$ 以及用户评论集合 $r=r_{1},...,r_{n}$ no   基于这些类别,我们可以正式概述用于生成条目简档的输入提示 $Q_{v}$ 的排列方式如下: 
$$Q_v(x) \text{ w.r.t.  } x = \begin{cases} [\alpha, \beta], & \text{if } \beta \text{ exists,} \\ [\alpha, \gamma, \text{cr}], & \text{otherwise.} \end{cases}. \quad (13)$$ 

 在我们的方法中,我们使用一个针对每个物品的函数 $f_{v}(\cdot)$ ,该函数将各种文本特征组合成一个字符串。   如果原始描述B缺失,我们会随机抽取一部分评论,并将它们与属性相结合作为输入。   通过纳入物品描述或用户评论,我们的提示信息为大语言模型提供了精确信息,确保生成的物品简介能准确反映其吸引人的特征。 

---

#### 3.2.3 用户提示构建。

 为了生成用户的画像,我们利用协作信息,假设我们已经预先生成了物品画像。   具体而言,我们将用户交互过的物品视为 $\mathcal{I}_{u}$,并均匀采样物品子集 $\hat{I}_{u}\subset I_{u}$ 对于 $\hat{I}_{u}$ 中的每个物品,我们将其文本属性拼接为 $c_{v}=[\alpha,\mathcal{P}_{v},r_{u}^{v}]$,其中 $r_{u}^{v}$ 表示用户提供的评论。   用于生成用户画像的输入提示 $Q_{u}$ 可定义如下: 
 $$Q_{u}=f_{u}(\{c_{v}|v\in\hat{I}_{u}\}). \quad (14)$$ 

 函数 $f_{u}(\cdot)$ 的作用与 $f_{v}(\cdot)\#(V)$,都是将文本内容组织成连贯的字符串。   每个文本属性,都包含用户评价,这些评价真实反映了他们的真实意见。   这种用户提示的构建方式为了解他们的真实偏好提供了有价值的见解。   由于篇幅限制,我们在附录 A.2 中包含了提示的详细设计,包括S, Q和 $f_{u/v}(\cdot)$,以及示例。 

---

### 3.3 用于互信息最大化的密度比建模

 在本节中,我们概述对密度比(记为 $f(s_{i},e_{i}))$ 进行建模的过程,目标是最大化互信息 $I(s_{i},e_{i})$。   首先,需要注意的是,我们之前已经生成了展示用户/物品交互偏好的用户/物品简档 $\mathcal{P}_{u/v}$。   因此,基于这些简档对语义表示进行如下编码是合理的: 
 $$s_{u}=\mathcal{T}(\mathcal{P}_{u})$ $s_{v}=\mathcal{T}(\mathcal{P}_{v}). \quad (15)$$ 

 这里, $\mathcal{T}(\cdot)$ 指的是一种被称为文本嵌入模型[14,29]的前沿技术,该技术已被证明能有效地将各种文本输入转换为保留其固有含义和上下文信息的定长向量。 

---

 根据文献[23],密度比 $f(s_{i},e_{i})$ 可被解释为一个正实值得分测量函数,用于捕捉 $s_{i}$ 和 $e_{i}$ 之间的相似性。   对密度比进行更精确的建模[28],可以对CF端理性表征与大语言模型增强的语义表征之间的对齐产生积极影响,有助于减轻表征学习中噪声信号的影响。   在此背景下,我们提出两种非常适合实现这一目标的建模方法对齐。   第一种方法是对比建模,该方法已被广泛验证 $[15,37]$,可有效地双向对齐不同视图,例如通过拉对和推对。   第二种方法是掩码重建生成式建模,它被广泛用作一种自监督机制,用于从数据本身重建部分掩码输入[10,13]。   通过使用CF侧表示来重建语义表示,我们可以有效地对齐这两种形式的信息。 

---

#### 3.3.1 对比对齐。

 如图3(b)所示,我们将 $f(s_{i},e_{i})$ 的具体实现表示为对比对齐。 
 $$f(s_{i},e_{i})=exp(sim(\sigma_{\perp}(s_{i}),e_{i})). \quad (16)$$

 函数 $sim(\cdot)$ 表示余弦相似度,而 $\sigma_{\perp}$ 表示一个多层感知器,它将语义表示 $s_{i}$ 映射到 $e_{i}$ 的特征空间中。   在我们的对比对齐中,我们将 $e_{i}$ 和s 视为正样本对。   在学习过程中,这些样本对会相互靠近以对齐它们的表示。   在具体实现中,目标是在一个批次内使正样本对更接近,同时将其余样本视为负样本。 

---

#### 3.3.2 生成式对齐。

 受近期关于掩码自编码器(MAE)研究的启发,MAE被认为是生成式自监督学习的一种范式,我们提出了一种在MAE中对密度比进行额外建模的方法。 
 $$f(s_{i},e_{i})=exp(sim(s_{i},\sigma_{\dagger}(\hat{e_{u}})))w.r.t.\hat{e_{i}}=\mathcal{R}(\{x\}\backslash x_{i}). \quad (17)$$ 

 我们采用 $\sigma_{1}$ 作为多层感知模型,将表示映射到语义特征空间 $x\backslash x_{i}$ 表示应用掩码后的第 个样本的初始嵌入。   生成过程遵循单向重建方法,专注于专门为掩码样本重建语义表示。   具体而言,掩码操作包括用指定的掩码标记(即 $[MASK])$ 替换初始嵌入,并且对用户/物品的一个随机子集进行掩码处理,随后进行重建。   这使我们能够探索语义特征空间内的重建能力。 

 通过我们的对比和生成对齐方法,我们有效地将大语言模型(LLM)的知识与理解用户偏好的领域进行了对齐。   这是通过将基于ID的协作关系信号与基于文本的行为语义相结合来实现的。   我们分别将提出的两种建模方法命名为RLMRec - Con和RLMRec - Gen。   在对真实世界数据进行的实验中,我们将全面评估这两种模型在各种任务中的性能,每种模型都展现出其独特的优缺点。 

---

### 3.4 模型无关学习

 到目前为止,我们的重点一直是优化协同过滤(CF)侧的关系表示和大语言模型(LLM)侧的语义表示s。   任何能够对用户/物品进行表示学习的模型都可以进行前面描述的优化过程。   因此,我们的方法是模型无关的,可以无缝增强现有的协同过滤推荐器。   假设推荐器R的优化目标表示为 $\mathcal{L}_{\mathbb{R}}$ ,我们的整体优化函数可表述如下: 
$$L= LR+Linfo \text{ w.r.t.  } Linfo-Blog[\frac{f(siei)}{\Sigma_{e\gamma \in s} f(sj,er)}]. \quad (18)$$ 

 最小化整体优化函数相当于最大化前文提到的互信息。 

---

## 4 评估

 本节通过多个数据集对我们的RLMRecon 进行实验评估, 以解决以下研究问题: 

*  **RQ1:** 在各种实验设置下,我们提出的RLMRec 是否优于现有的最先进推荐器? 
*  **RQ2:** 大语言模型增强的语义表示是否有助于提升推荐性能? 
*  **研究问题3:** 我们提出的框架是否能通过跨视图语义对齐有效解决噪声数据问题? 
*  **研究问题4:** 作为提升推荐系统性能的预训练框架,我们的模型有多大潜力? 
*  **研究问题5:** 我们的RLMRec在训练效率方面表现如何? 

---

### 4.1 实验设置

#### 4.1.1 数据集。

 我们在三个公开数据集上对我们的RLMRec进行评估: 
*  **亚马逊图书:** 该数据集包含用户对亚马逊上销售书籍的评分和相应评论。 
*  **Yelp:** 该数据集是一个用户-商家数据集,提供了各类商家的大量文本类别信息。 
*  **Steam:** 该数据集包含用户对Steam平台上电子游戏给出的文本反馈。 
 遵循[35,42,44]中类似的数据预处理设置,我们过滤掉亚马逊图书和Yelp数据中评分低于3的交互。   由于Steam数据集没有评分,因此不对其进行过滤。   然后我们进行k -核过滤,并以3:1:1的比例将每个数据集划分为训练集、验证集和测试集。   数据集统计信息总结请参考附录中的表5。 

---

#### 4.1.2 评估协议与指标。

 为确保全面评估并减少偏差,我们在所有项目中采用全排名协议 11,36,37来准确评估我们的推荐结果。   我们使用两种广泛采用的基于排名的指标:Recall@N和NDCG@N,用于衡量模型的有效性。 

---

#### 4.1.3 基础模型。

 我们通过将RLM-Rec与基于 SSLRec [25]的最先进的基于表示的推荐器集成,来评估其有效性。 

*  **GCCF [6]:** 它通过重新评估图神经网络(GNNs)中非线性操作的作用,简化了基于图的推荐器设计。 
*  **LightGCN [11]:** 它通过简化图消息传递中冗余的神经模块,创建了一个轻量级的推荐器。 
*  **SGL [37]:** 它利用节点/边丢弃作为数据增强器,为对比学习生成多样化的视角。 
*  **SimGCL [44]:** 它通过引入无增强视图生成技术来提升推荐性能。 
*  **DCCF [26]:** 它利用解纠缠对比学习捕捉用于推荐目的的意图关系。 
*  **AutoCF [41]:** 它是一种自监督的掩码自编码器,用于自动执行推荐的数据增强过程。 

---

#### 4.1.4 实现细节。

 所有基础模型的表示维度(即x和e) 均设置为32。   我们通过网格搜索为每个模型确定超参数。   为了生成用户和物品特征,我们利用OpenAI提供的 ChatGPT 模型(具体为gpt-3.5-turbo)。   我们使用 text - embedding - ada-002 [22] 来生成语义表示s。   在训练期间,所有方法均使用 Adam 优化器,以4096 的固定批量大小和1e-3的学习率进行训练。   我们根据模型在验证集上的性能采用早停技术。 

---

### 4.2 性能比较(研究问题1)

 **模型无关的性能提升。** 为了证明RLMRec在提高推荐性能方面的有效性,我们将其集成到六个最先进的协同过滤模型中。   我们使用5次随机初始化进行实验,并在表1中报告平均结果。   评估结果揭示了几个有趣的现象,如下所述: 

*  总体而言,我们始终观察到,与原始版本相比,将 RLMRec与骨干推荐器集成可提高性能。   这为RLMRec的有效性提供了有力证据。   我们将这些改进归因于两个关键因素:i) RLMRec能够借助大语言模型实现准确的用户/物品画像,增强从用户交互行为中提取的丰富语义信息的表示。   ii) 我们的跨视图互信息最大化促进了协同过滤侧关系嵌入和大语言模型侧语义表示的协同增强,有效过滤了推荐特征中的无关噪声。 
*  显然,对比建模和生成建模方法通常都能提升性能。   然而,需要注意的是,当与GCCF和SimGCL等各种骨干网络结合使用时,对比方法表现出更优的性能。   相反,当应用于涉及掩码重建的AutoCF时,RLMRec - Gen显示出更显著的改进。   我们推测,掩码操作起到了一种正则化的作用,因此在与采用生成方法的模型结合使用时能产生更好的结果。 

---

 **相对于大语言模型增强方法的优越性。** 此外,我们对 RLMRec与KAR [40] (一种近期的大语言模型增强的用户行为建模方法)的有效性进行了比较评估。   KAR旨在生成文本形式的用户/物品描述,以增强点击率任务中用户偏好的学习。   为确保公平比较,我们采用了与我们方法相同的语义表示,并使用两种经典方法(LightGCN和SGL)作为骨干模型。   这可能是因为,虽然KAR将文本信息融入用户偏好学习,但它将语义表示作为模型的输入特征。   因此,它可能无法有效地将文本知识与用户行为表示对齐,并且更容易受到来自用户行为或大语言模型知识库的无关噪声的影响。 

---

### 4.3 消融实验(研究问题2)

 在本节中,我们研究了整合语义表示对性能的影响。   为此,我们对获取的语义表示,导致与协同关系表示和大语言模型知识之间出现不一致。   我们使用默认的语义编码模型text-embedding - ada - 002 [2],并对Contriever [14] 和Instructor [29]等先进模型进行了实验。   我们在四种骨干方法(即LightGCN、GCCF、SimGCL和DCCF)上评估了我们的方法。   结果总结在图4中,得出两个关键观察结果。 

 在随机重新排列语义表示以破坏协同信号和语义信号之间的相关性之后,我们观察到,在评估的骨干模型上,RLMRec-Con和 RLMRec-Gen的性能均有所下降。 [cite: 296, 297]  这表明,由于语义信息和协同信息不匹配,打乱后的表示引入了噪声。   这证明了大语言模型(LLM)的语义知识与用户之间的协同关系准确对齐,对于提升推荐性能至关重要。 

 当我们使用Contriever和Instructor等不同的文本嵌入模型时,我们的RLMRec仍能提升基础性能,这与使用text- embedding-ada-002的默认设置类似。   这表明,我们的 RLMRec能够有效利用合适的文本编码器,将文本语义转化为偏好表示,从而提升推荐骨干模型的性能。   此外,文本嵌入模型更准确地捕捉语义信息的能力,能够带来更显著的性能提升。 

---



---

### 4.4 RLMRec的深入分析(研究问题3-研究问题5)

#### 4.4.1 关于噪声数据的性能(研究问题3)。

 我们通过向原始训练数据中添加不存在的交互来评估RLMRec对数据噪声的鲁棒性。   噪声水平相对于训练集大小的范围为5%至25%。   使用亚马逊数据集,我们比较了普通LightGCN与由我们的RLMRec-Con/Gen增强的LightGCN的性能。   图5的主要发现如下: 

* (i)  在所有噪声水平下,RLMRec-Con和RLMRec-Gen始终优于LightGCN骨干模型。   这凸显了融入语义信息并利用互信息过滤无关数据的优势,从而提高了推荐效果和抗噪声能力。 
* (ii)  与RLMRec-Gen相比,RLMRec-Con对数据噪声表现出更好的抗性。   这可能是由于生成方法通过节点掩码引入了固有噪声。   相比之下,对比方法遇到的噪声较少,因此在相同噪声比率下性能更优。 

---


---

#### 4.4.2 预训练场景下的性能(研究问题4)。

 我们探索了将我们的语义参与训练机制作为下游模型的预训练技术的潜力。   使用Yelp数据集,我们利用2012年至2017年的数据进行预训练,并将2018年至2019年的数据划分为训练集、验证集和测试集(下游数据集)。   两个数据集包含相同的用户和物品。   我们在预训练数据集上训练原始LightGCN和我们的模型。   学习到的参数用于初始化原始LightGCN的嵌入,然后在下游数据集上对其进行训练。   表3的主要发现如下: 

*  与不进行预训练相比,使用参数进行预训练能产生更优的结果,无论预训练是使用基础模型还是我们的RLMRec进行的。   这表明预训练数据集包含有价值的协同信息,有助于预测用户/物品偏好并有益于下游任务。 
*  与仅使用基础模型进行预训练相比,RLMRec-Con和 RLMRec-Gen都能带来更好的预训练效果,其中RLMRec- Gen取得了最佳结果。   这凸显了在预训练场景中融入语义信息的优势以及生成式方法的有效性,这可能是由于掩码操作的调节作用,防止了在预训练数据集上的过拟合。 

---

#### 4.4.3 训练效率分析(研究问题5)。

 我们分析了使用RLMRec 的时间复杂度。   理论时间复杂度对于RLMRec-Con和RLMRec-Gen,多层感知机(和)的时间复杂度为 $O(N\times d_{s}\times d_{e})$。   对于RLMRec-Con,损失计算引入了额外的复杂度 $\mathcal{O}(N^{2}\times d)$。   对于RLMRec-Gen,时间复杂度为O $(M\times d+M\times N\times d)$,其中掩码操作占M×d, M表示掩码节点的数量。   在表4中,我们展示了在配备英特尔至强银牌4314 CPU和英伟达RTX 3090 GPU的服务器上进行训练的轮次时间。   结果表明,RLMRec-Gen的时间成本始终低于RLMRec-Con。   这主要是因为RLMRec-Con中的值由批量大小决定,而批量大小往往大于RLMRec-Gen中掩码节点的数量M。   此外,对于性能有所提升的更大模型,与原始时间相比,额外的时间复杂度仅约为10%到20%。 

---

### 4.5 案例研究

 我们探索了大语言模型(LLM)增强语义的集成,以捕捉那些难以通过直接消息传递捕捉到的全局用户关系。   图6展示了一个案例研究,其中用户41998和4227之间的距离超过了3跳。   为了评估模型捕捉他们之间关系的能力,我们检查了用户表示的相似性。   我们比较了LightGCN和RLMRec - Con,两者使用相同的骨干网络。   引入了两个指标:用户41998的相关性得分以及基于该得分的其长距离邻居(>3hops)的排名。   通过纳入从大语言模型中得出的语义信息,例如41998 和 4227 之间的共同兴趣(例如,友好的服务),我们观察到相关性得分和排名都有所提高。   这表明,RLMRec学习到的表示能够捕捉到基于ID的推荐技术之外的全局协作关系。 

---


---

## 5 结论

 本文提出了RLMRec,这是一个与模型无关的框架,它利用大语言模型(LLM)来提高推荐系统的表征性能。   我们引入了一种协作式用户画像生成范式和一种推理驱动的系统提示,强调在生成的输出中包含推理过程。   RLMRec利用对比和生成对齐技术,将协同过滤(CF)侧的关系嵌入与大语言模型侧的语义表征对齐,有效降低了特征噪声。   该框架结合了通用推荐器和大语言模型的优势,并得到了可靠的理论保证,且在真实世界的数据集上进行了广泛评估。   我们未来的研究将专注于通过提供更具洞察力的解释,推动基于大语言模型的推理结果在推荐系统中的应用。 