/* @theme left-align */

@charset "UTF-8";
/* 移除默认主题导入，避免居中冲突 */
/* @import-theme 'default'; */

/*
 * Left-Align Theme for Marp
 * 专门用于左上对齐布局，同时支持数学公式居中显示
 * 修复版本 - 不依赖默认主题，完全自定义
 */

/* 基础重置样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: "Microsoft YaHei", "SimHei", sans-serif;
}

/* 重置所有可能的默认样式 - 强制覆盖Marp的内置居中 */
section {
  /* 强制布局重置 - 覆盖Marp默认行为 */
  display: flex !important;
  flex-direction: column !important;
  justify-content: flex-start !important;
  align-items: flex-start !important;

  /* 尺寸和位置 - 强制占满全屏 */
  width: 100vw !important;
  height: 100vh !important;
  min-height: 100vh !important;
  max-height: 100vh !important;
  padding: 50px 80px !important;
  margin: 0 !important;
  box-sizing: border-box !important;

  /* 强制定位 - 覆盖任何居中机制 */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;

  /* 文本和字体 */
  text-align: left !important;
  line-height: 1.25 !important;
  font-family: "Microsoft YaHei", "SimHei", sans-serif !important;

  /* 背景和其他 */
  background: white !important;
  z-index: 1 !important;

  /* 禁用任何可能的居中机制 */
  place-items: start !important;
  place-content: start !important;
  justify-items: start !important;
  align-content: flex-start !important;

  /* 强制内容从顶部开始 */
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

/* 强制所有页面的子元素左对齐 - 更强的选择器 */
section > *,
section * {
  text-align: left !important;
  margin-left: 0 !important;
  margin-right: auto !important;
  justify-self: start !important;
  align-self: flex-start !important;
}

/* 所有页面的标题样式 - 强制左对齐 */
section h1,
section h2,
section h3,
section h4,
section h5,
section h6 {
  text-align: left !important;
  margin-bottom: 20px !important;
  margin-left: 0 !important;
  margin-right: auto !important;
  width: 100% !important;
}

/* 第一页标题页特殊样式 - 覆盖所有左对齐设置 */
section:first-of-type,
section:first-child {
  text-align: center !important;
  justify-content: center !important;
  align-items: center !important;
  place-items: center !important;
  place-content: center !important;

  /* 第一页也使用固定定位，但居中 */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 第一页的所有子元素居中 - 更强的选择器 */
section:first-of-type *,
section:first-of-type > *,
section:first-child *,
section:first-child > * {
  text-align: center !important;
  margin-left: auto !important;
  margin-right: auto !important;
  justify-self: center !important;
  align-self: center !important;
}

/* 第一页的标题居中 - 多重选择器确保生效 */
section:first-of-type h1,
section:first-of-type h2,
section:first-of-type h3,
section:first-child h1,
section:first-child h2,
section:first-child h3 {
  text-align: center !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* 数学公式居中显示 - 强化版本 */
.MathJax,
.MathJax_Display,
mjx-container[display="true"],
mjx-container[jax="CHTML"][display="true"],
.mjx-chtml[display="true"],
div[style*="text-align: center"] {
  text-align: center !important;
  margin-left: auto !important;
  margin-right: auto !important;
  display: block !important;
  width: 100% !important;
  justify-self: center !important;
  align-self: center !important;
}

/* 行内数学公式保持原样 */
mjx-container[display="false"],
mjx-container[jax="CHTML"][display="false"],
.mjx-chtml[display="false"] {
  display: inline !important;
  text-align: inherit !important;
}

/* 确保包含$$公式的段落居中 - 更强的选择器 */
p:has(mjx-container[display="true"]),
div:has(mjx-container[display="true"]),
section p:has(mjx-container[display="true"]),
section div:has(mjx-container[display="true"]) {
  text-align: center !important;
  margin-left: auto !important;
  margin-right: auto !important;
  width: 100% !important;
}

/* 作者信息区域保持居中 */
.authors-grid {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 15px !important;
  margin: 20px 0 !important;
  text-align: center !important;
  justify-items: center !important;
  align-items: center !important;
  width: 100% !important;
  justify-self: center !important;
  align-self: center !important;
}

.author {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
  justify-self: center !important;
  align-self: center !important;
}

.author-name {
  font-weight: bold !important;
  margin-bottom: 5px !important;
  font-size: 1.1em !important;
  text-align: center !important;
}

.author-affiliation {
  font-size: 0.9em !important;
  color: #666 !important;
  text-align: center !important;
}

/* 段落样式 */
section p {
  margin-bottom: 10px !important;
  line-height: 1.25 !important;
}

/* 列表样式 */
section ul,
section ol {
  margin-left: 20px !important;
  margin-bottom: 10px !important;
}

section li {
  margin-bottom: 5px !important;
  line-height: 1.25 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .authors-grid {
    grid-template-columns: 1fr !important;
  }
}

/* 特殊页面类 - 确保左上对齐 */
section.left-top {
  text-align: left !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: flex-start !important;
  align-items: flex-start !important;
  place-items: start !important;
  place-content: start !important;
}

section.left-top * {
  text-align: left !important;
  margin-left: 0 !important;
  margin-right: auto !important;
  justify-self: start !important;
  align-self: flex-start !important;
}

section.left-top h1,
section.left-top h2,
section.left-top h3,
section.left-top h4,
section.left-top h5,
section.left-top h6 {
  text-align: left !important;
  margin-left: 0 !important;
  margin-right: auto !important;
}