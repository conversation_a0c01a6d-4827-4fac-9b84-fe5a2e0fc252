/* @theme suibeppt */

@charset "UTF-8";
/* 移除默认主题导入，避免居中冲突 */
/* @import-theme 'default'; */

/*
 * Suibeppt Theme for Marp
 * 修复版本 - 确保左上对齐布局正常工作
 * 保持原有的字体大小和颜色设置
 * 不依赖默认主题，完全自定义
 */

/* 基础重置样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: Arial, Helvetica, sans-serif;
}

/* 基础section样式 - 强制覆盖Marp的内置居中 */
section {
    /* 字体和文本设置 */
    font-size: 25px !important;
    letter-spacing: 2px !important;
    font-family: Arial, Helvetica, sans-serif !important;
    line-height: 1.25 !important;

    /* 强制布局重置 - 覆盖Marp默认行为 */
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
    align-items: flex-start !important;

    /* 尺寸和位置 - 强制占满全屏 */
    width: 100vw !important;
    height: 100vh !important;
    min-height: 100vh !important;
    max-height: 100vh !important;
    padding: 50px 80px 40px 80px !important;
    margin: 0 !important;
    box-sizing: border-box !important;

    /* 强制定位 - 覆盖任何居中机制 */
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;

    /* 文本对齐 */
    text-align: left !important;

    /* 背景和其他 */
    background: white !important;
    z-index: 1 !important;

    /* 禁用任何可能的居中机制 */
    place-items: start !important;
    place-content: start !important;
    justify-items: start !important;
    align-content: flex-start !important;

    /* 重置变换 */
    transform: none !important;
    translate: none !important;

    /* 强制内容从顶部开始 */
    overflow-y: auto ;
    overflow-x: hidden !important;
}

/* 强制所有页面的子元素左对齐 */
section > *,
section * {
    text-align: left !important;
    margin-left: 0 !important;
    margin-right: auto !important;
    justify-self: start !important;
    align-self: flex-start !important;
}

/* 第一页（标题页）特殊处理 - 保持居中 */
section:first-child,
section:first-of-type {
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
    place-items: center !important;
    place-content: center !important;

    /* 第一页也使用固定定位，但居中 */
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;

    /* 覆盖上面的padding设置 */
    padding: 50px 80px !important;
}

/* 第一页的所有子元素居中 */
section:first-child *,
section:first-child > *,
section:first-of-type *,
section:first-of-type > * {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    justify-self: center !important;
    align-self: center !important;
}

/* 标题样式 - 修复版本 */
h1 {
    color: rgb(45,67,100) !important;
    margin-top: 20px !important;
    margin-bottom: 20px !important;
    font-size: 50px !important;
    line-height: 80px !important;
    /* 移除居中设置，让section的规则控制对齐 */
    text-align: inherit !important;
    width: 100% !important;
}

/* 第一页的h1特殊处理 - 确保居中 */
section:first-child h1,
section:first-of-type h1 {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

/* 非第一页的h1确保左对齐 */
section:not(:first-child) h1,
section:not(:first-of-type) h1 {
    text-align: left !important;
    margin-left: 0 !important;
    margin-right: auto !important;
}

h2 {
    text-align: left !important;
    margin-top: 12px !important;
    font-size: 30px !important;
    line-height: 25px !important;
    margin-bottom: 15px !important;
    width: 100% !important;
}

h3 {
    text-align: left !important;
    margin-top: 10px !important;
    margin-bottom: 12px !important;
    font-size: 27px !important;
    line-height: 25px !important;
    width: 100% !important;
}

h4 {
    text-align: left !important;
    margin-top: 10px !important;
    margin-bottom: 12px !important;
    font-size: 27px !important;
    line-height: 25px !important;
    width: 100% !important;
}

h5, h6 {
    text-align: left !important;
    margin-top: 10px !important;
    margin-bottom: 12px !important;
    font-size: 25px !important;
    line-height: 25px !important;
    width: 100% !important;
}

p {
    text-align: left !important;
    font-size: 22px !important;
    margin-top: 7px !important;
    margin-bottom: 10px !important;
    letter-spacing: 1px !important;
    line-height: 1.25 !important;
    width: 100% !important;
    /* text-indent: 50px; */
}

/* 列表样式 */
ul, ol {
    text-align: left !important;
    margin-left: 20px !important;
    margin-bottom: 10px !important;
    width: calc(100% - 20px) !important;
}

li {
    text-align: left !important;
    margin-bottom: 5px !important;
    line-height: 1.25 !important;
}

table {
    text-align: center !important;
    border: 2px solid rgb(140 140 140) !important;
    margin: 10px auto !important;
    width: 100% !important;
}

/* 数学公式居中显示 */
.MathJax,
.MathJax_Display,
mjx-container[display="true"],
mjx-container[jax="CHTML"][display="true"],
.mjx-chtml[display="true"] {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    display: block !important;
    width: 100% !important;
    justify-self: center !important;
    align-self: center !important;
}

/* 行内数学公式保持原样 */
mjx-container[display="false"],
mjx-container[jax="CHTML"][display="false"],
.mjx-chtml[display="false"] {
    display: inline !important;
    text-align: inherit !important;
}

/* 确保包含$$公式的段落居中 */
p:has(mjx-container[display="true"]),
div:has(mjx-container[display="true"]),
section p:has(mjx-container[display="true"]),
section div:has(mjx-container[display="true"]) {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    width: 100% !important;
}

/* 作者信息区域保持居中 */
.authors-grid {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 15px !important;
    margin: 20px 0 !important;
    text-align: center !important;
    justify-items: center !important;
    align-items: center !important;
    width: 100% !important;
    justify-self: center !important;
    align-self: center !important;
}

.author {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
    justify-self: center !important;
    align-self: center !important;
}

.author-name {
    font-weight: bold !important;
    margin-bottom: 5px !important;
    font-size: 1.1em !important;
    text-align: center !important;
}

.author-affiliation {
    font-size: 0.9em !important;
    color: #666 !important;
    text-align: center !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .authors-grid {
        grid-template-columns: 1fr !important;
    }
}

/* 特殊页面类 - 确保左上对齐 */
section.left-top {
    text-align: left !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
    align-items: flex-start !important;
    place-items: start !important;
    place-content: start !important;
}

section.left-top * {
    text-align: left !important;
    margin-left: 0 !important;
    margin-right: auto !important;
    justify-self: start !important;
    align-self: flex-start !important;
}

section.left-top h1,
section.left-top h2,
section.left-top h3,
section.left-top h4,
section.left-top h5,
section.left-top h6 {
    text-align: left !important;
    margin-left: 0 !important;
    margin-right: auto !important;
}








